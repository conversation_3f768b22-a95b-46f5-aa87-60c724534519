# JSX

## 概念
- JSX 是 JavaScript 和 XML（HTML）的缩写，表示在JS代码中编写HTML模版结构，它是React中编写UI模版的方式。

## 优势
- HTML的声明式模版写法
- JS的可编程能力

## 本质
- JSX 并不是标准的JS语法，它是JS的语法扩展，浏览器本身不能识别，需要通过解析工具做解析之后才能在浏览器中运行。

## 使用

### jsx表达式
- 在 JSX 中可以通过大括号语法 {} 识别JS表达式，比如常见的变量、函数调用、方法调用等。
    - 使用引号传递字符串：`{'this is a message'}`
    - 使用 JavaScript 变量：`{count}`
    - 函数调用：`{getName()}`
    - 方法调用：`{new Date().getDate()}`
    - 使用 JavaScript 对象：`<div style={{color: 'red' }}>this is div</div>`
- 注意：if语句、switch语句、变量声明属于语句，不是表达式，不能出现在 {} 中。

### 列表渲染
- 在JSX中可以使用原生JS中的map方法遍历渲染列表。
    - map循环哪个结构，就return哪个结构。
    - 注意需要加上一个独一无二的key，可以是字符串或num格式，如id。
    - key的作用：react框架内部使用，可以提升更新性能。
- 示例：`<ul>{list.map(item=><li key={item.id}>{item.name}</li>)}</ul>`

### 条件渲染
- 语法：在react中，可以用过逻辑与运算符&&，三元表达式 (?:) 实现基础的条件渲染
- 示例
    - `{islogin && <span>this is span</span>}`
    - `{islogin ? <span>this is span</span> : <span>login...</span>}`
- 复杂条件渲染：自定义函数 + if判断
