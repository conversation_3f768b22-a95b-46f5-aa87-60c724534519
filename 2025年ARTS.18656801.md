# ARTS 打卡第 32 周（8.25-8.31）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 31 周（8.18-8.24）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 30 周（8.11-8.17）

> A: Algorithm 一道算法题

[leetcode-643 子数组最大平均数 I](https://www.cnblogs.com/505donkey/p/19029398)

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 29 周（8.4-8.10）

> A: Algorithm 一道算法题

[leetcode-904 水果成篮](https://www.cnblogs.com/505donkey/p/19022369)

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[滑动窗口](https://www.cnblogs.com/505donkey/p/19024259)

> S: Share 分享一个观点

技术人的核心能力，是在工具迭代中持续识别不可替代的元能力。

# ARTS 打卡第 28 周（7.28-8.3）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 27 周（7.21-7.27）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 26 周（7.14-7.20）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 25 周（7.7-7.13）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 24 周（6.30-7.6）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 23 周（6.23-6.29）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[3-2 如何把能力产品化](https://www.cnblogs.com/505donkey/p/18951073)

# ARTS 打卡第 22 周（6.16-6.22）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[1-2 如何打造一家一人公司](https://www.cnblogs.com/505donkey/p/18951191)

# ARTS 打卡第 21 周（6.9-6.15）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[1-3 实现一人公司需要具备哪些动机和方法](https://www.cnblogs.com/505donkey/p/18955414)

# ARTS 打卡第 20 周（6.2-6.8）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[1-4 把自己当成公司来经营](https://www.cnblogs.com/505donkey/p/18955421)

# ARTS 打卡第 19 周（5.26-6.1）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[iperf 安装与使用](https://www.cnblogs.com/505donkey/p/18896478)

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 18 周（5.19-5.25）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[1-5 再小的个体，也可以是一家公司](https://www.cnblogs.com/505donkey/p/18956963)

# ARTS 打卡第 17 周（5.12-5.18）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 16 周（5.5-5.11）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 15 周（4.28-5.4）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 14 周（4.21-4.27）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 13 周（4.14-4.20）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

duolingo

> T: Technique/Tips 分享一个小技术

[linux 查看磁盘是否为 SSD](https://www.cnblogs.com/505donkey/p/18812800)

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 12 周（4.7-4.13）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[--]()

> T: Technique/Tips 分享一个小技术

[Vite 简介及常见使用](https://www.cnblogs.com/505donkey/p/18792653)

> S: Share 分享一个观点

[未来，什么样的孩子不会被 AI 取代？](https://www.cnblogs.com/505donkey/p/18808359)

# ARTS 打卡第 11 周（3.31-4.6）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[2025 Zhongguancun Forum to be held in late March 2025 中关村论坛将于 3 月下旬举办](https://www.cnblogs.com/505donkey/p/18787984)

> T: Technique/Tips 分享一个小技术

[磁盘性能测试工具 FIO 安装与使用](https://www.cnblogs.com/505donkey/p/18742275)
细化安装部分

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 10 周（3.24-3.30）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[TCL boosts AI integration in consumer electronics and home appliances TCL 加强 AI 在消费电子和家电领域的集成](https://www.cnblogs.com/505donkey/p/18787993)

> T: Technique/Tips 分享一个小技术

[node 包管理工具 npm、yarn 比较](https://www.cnblogs.com/505donkey/p/18790363)

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 11 周（3.17-3.23）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[Rural tourism thriving in Anhui ancient town 安徽古镇乡村旅游蓬勃发展](https://www.cnblogs.com/505donkey/p/18609324)

> T: Technique/Tips 分享一个小技术

性能自动化测试平台设计方案 V1

> S: Share 分享一个观点

[--]()

# ARTS 打卡第 10 周（3.10-3.16）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[Apple CEO Tim Cook arrives in Beijing 苹果 CEO Tim Cook 抵达北京](https://www.cnblogs.com/505donkey/p/18787982)

> T: Technique/Tips 分享一个小技术

[mermaid 概念与常见用法介绍](https://www.cnblogs.com/505donkey/p/18760483)

> S: Share 分享一个观点
> qecon 分享主题大纲（DeepSeek 赋能探索-安全制造场景下的性能自动化测试平台工程：合规驱动与 AI 协同实践）

# ARTS 打卡第 9 周（3.3-3.9）

> A: Algorithm 一道算法题

[leetcode-222 完全二叉树的节点个数](https://www.cnblogs.com/505donkey/p/18625320)

> R: Review 读一篇英文文章

[Forum: Sino-EU cooperation pertinent for AI regulation 论坛：中欧人工智能监管合作的重要性](https://www.cnblogs.com/505donkey/p/18611907)

> T: Technique/Tips 分享一个小技术

[cephadm shell 和 ceph 区别](https://www.cnblogs.com/505donkey/p/18748251)

> S: Share 分享一个观点

[1-1 什么是一人公司](https://www.cnblogs.com/505donkey/p/18746099)

# ARTS 打卡第 8 周（2.24-3.2）

> A: Algorithm 一道算法题

[leetcode-637 二叉树的层平均值](https://www.cnblogs.com/505donkey/p/18625323)

> R: Review 读一篇英文文章

[China issues guideline on smart infrastructure 中国发布智能基础设施指南](https://www.cnblogs.com/505donkey/p/18611903)

> T: Technique/Tips 分享一个小技术

[fanotify 介绍](https://www.cnblogs.com/505donkey/p/18250217)

> S: Share 分享一个观点

[08 许愿池](https://www.cnblogs.com/505donkey/p/18691890)

# ARTS 打卡第 7 周（2.17-2.23）

> A: Algorithm 一道算法题

[leetcode-530 二叉搜索树的最小绝对差](https://www.cnblogs.com/505donkey/p/18625326)

> R: Review 读一篇英文文章

[China home to 4.1 million 5G base stations 中国拥有 410 万个 5G 基站](https://www.cnblogs.com/505donkey/p/18611914)

> T: Technique/Tips 分享一个小技术

[02 深度学习基本概念简介](https://www.cnblogs.com/505donkey/p/18682662)

> S: Share 分享一个观点

[07 凡墙皆是门](https://www.cnblogs.com/505donkey/p/18691886)

# ARTS 打卡第 6 周（2.10-2.16）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[Alibaba used goods trading platform ramps up AI efforts 阿里巴巴二手交易平台加大人工智能投入](https://www.cnblogs.com/505donkey/p/18611921)

> T: Technique/Tips 分享一个小技术

[麒麟 V10 离线搭建 Go 环境](https://www.cnblogs.com/505donkey/p/18721875)

> S: Share 分享一个观点

[06 好消息藏在最凶狠的对手 AI 身上](https://www.cnblogs.com/505donkey/p/18691889)

# ARTS 打卡第 5 周（2.3-2.9）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[Interview: China 'winning big' in tech sector, says industry expert 采访：据行业专家表示，中国在科技领域“大获全胜”。](https://www.cnblogs.com/505donkey/p/18611925)

> T: Technique/Tips 分享一个小技术

[03 机器学习任务攻略](https://www.cnblogs.com/505donkey/p/18721110)

> S: Share 分享一个观点

[05 好消息藏在寂寞处](https://www.cnblogs.com/505donkey/p/18691885)

# ARTS 打卡第 4 周（1.27-2.2）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[China's news industry enters era of digital, intelligent integration: report 中国新闻行业进入数字化，智能化融合时代：报导](https://www.cnblogs.com/505donkey/p/18652100)

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[04 好消息藏在新关系里](https://www.cnblogs.com/505donkey/p/18691844)

# ARTS 打卡第 3 周（1.20-1.26）

> A: Algorithm 一道算法题

[leetcode-67 二进制求和](https://www.cnblogs.com/505donkey/p/18625331)

> R: Review 读一篇英文文章

[China's new AI model claims to beat competitors 中国新的人工智能模型宣称能击败竞争对手](https://www.cnblogs.com/505donkey/p/18652107)

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[03 好消息藏在确定性中](https://www.cnblogs.com/505donkey/p/18681014)

# ARTS 打卡第 2 周（1.13-1.19）

> A: Algorithm 一道算法题

[leetcode-190 颠倒二进制位](https://www.cnblogs.com/505donkey/p/18625333)

> R: Review 读一篇英文文章

[Chinese large model player Zhipu AI raises $420m 中国大模型公司智谱 AI 融资 4.2 亿美元](https://www.cnblogs.com/505donkey/p/18634382)

> T: Technique/Tips 分享一个小技术

[01 Deep Learning for Human Language Processing（Course Overview）](https://www.cnblogs.com/505donkey/p/18660763)

> S: Share 分享一个观点

[01 十年怕与爱](https://www.cnblogs.com/505donkey/p/18674282)

# ARTS 打卡第 1 周（1.6-1.12）

> A: Algorithm 一道算法题

[--]()

> R: Review 读一篇英文文章

[China to accelerate 5G revolution, 6G innovation in 2025 中国将在 2025 年加速 5G 改革和 6G 创新](https://www.cnblogs.com/505donkey/p/18652109)

> T: Technique/Tips 分享一个小技术

[--]()

> S: Share 分享一个观点

[02 好消息藏在水面下](https://www.cnblogs.com/505donkey/p/18675508)
