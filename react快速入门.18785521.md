## 一、基本介绍

React是由Facebook开发的开源JavaScript库，专注于构建高效、灵活的用户界面。

其核心特性包括：

- ​组件化开发：将UI拆分为独立、可复用的组件（如按钮、表单等）。
- ​虚拟DOM：通过内存中的虚拟DOM优化渲染性能，减少真实DOM操作。
- JSX语法：JavaScript语法扩展，允许HTML与JS混写。
- Hooks机制：函数式组件中管理状态和生命周期（如useState、useEffect）。

## 二、环境搭建

### 1 安装 Node.js

[官网下载地址](https://nodejs.org/zh-cn)

`Node.js` 是 React 项目运行的基础环境，因此需要先进行安装，安装版本优先 LTS（稳定，适合生产环境使用）。

安装完毕后，在终端命令行输入以下目录进行验证。

```bash
node -v  # 查看 Node.js 版本
npm -v   # 查看 npm 版本
```

**【说明】配置源**
`npm` 是 Node.js 的包管理工具，默认安装后会自带，默认的包源（registry）是 [https://registry.npmjs.org/](https://registry.npmjs.org/)，但由于网络原因，国内访问可能会很慢。所以一般推荐改成国内镜像源（如 [淘宝源：https://registry.npmmirror.com](https://registry.npmmirror.com)）。

具体修改可参考：[node包管理工具 npm、yarn比较](https://www.cnblogs.com/505donkey/p/18790363)

### 2 创建 React 项目

完成运行环境搭建之后，可以使用 `create-react-app` 来快速创建一个新的 React 项目。`create-react-app` 是 React 官方提供的脚手架工具，它配置好了开发环境，包括 Webpack、Babel、ESLint 等，让用户可以专注于编写代码，而无需手动配置复杂的构建工具。

```bash
# 创建项目
npx create-react-app my-app
# 进入项目目录
cd my-app
# 启动开发服务器
npm start
```
**【说明】**
- `npx`：是 `npm 5.2.0` 及以上版本自带的一个工具，用于运行本地或远程的 npm 包。它不需要全局安装 `create-react-app`，而是直接下载并运行最新版本的 `create-react-app`。
- `my-app`：项目名称，可以根据用户需要进行自定义修改。
- `npm start`：这个命令会启动一个开发服务器，并自动打开浏览器（访问地址为 http://localhost:3000）。
- 开发服务器：支持热重载功能，即用户修改代码保存后，页面会自动刷新，无需手动重启前端服务。

### 3 项目结构解析

**初始目录结构**
```markdown
my-app/
├── node_modules/      # 项目依赖的第三方库
├── public/            # 静态资源文件夹
│   ├── index.html     # 应用的 HTML 模板（React会将组件渲染到这个文件中）
│   └── ...            # 其他静态资源（如图片、字体）
├── src/               # 源代码文件夹
│   ├── App.css        # App 组件的样式文件
│   ├── App.js         # App 根组件（用户可以在这里引入编写自己的组件）
│   ├── index.css      # 全局样式文件
│   ├── index.js       # 应用的入口文件（React会从这里开始渲染整个应用）
│   └── ...            # 其他组件或文件
├── package.json       # 项目的配置与依赖声明文件
└── README.md          # 项目的说明文档
```

**优化目录结构**
```markdown
my-app/
├── node_modules/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   ├── robots.txt
│   └── ...
├── src/
│   ├── App.css
│   ├── App.js
│   ├── index.css
│   ├── index.js
│   ├── utils/         # 工具函数目录
|   |   ├── api/       # 请求封装
|   |   ├── helpers/   # 通用辅助函数
|   │   └── ...
│   ├── components/    # 自定义用户组件目录
|   |   ├── common/    # 通用组件（按钮、输入框等）
|   |   └── ...        # 其他功能模块目录
│   ├── assets/        # 代码关联静态资源目录
|   │   ├── images/    # 关联图片资源
|   │   ├── fonts/     # 关联字体资源
|   │   ├── styles/    # 关联组件样式
|   │   └── ...        # 其他关联静态资源
│   ├── routes/        # 路由配置，如 `React Router`
│   ├── store/         # 状态管理目录，如 `Redux`，`Zustand`
│   ├── hooks/         # 自定义Hooks目录
│   ├── __tests__/     # 测试文件目录
|   │   ├── App.test.js # App 组件测试（至少保留核心组件测试用例）
|   │   └── ...
│   ├── docs/          # 存放项目文档（如 API 说明、设计规范）
│   └── ... 
├── .env               # 默认环境变量，适用于所有环境
├── .env.development   # 开发环境变量
├── .env.production    # 生产环境变量
├── .env.local         # 本地覆盖配置，优先级最高
├── .eslintrc          # ESLint 代码规范配置
├── .gitignore         # 版本控制忽略
├── jsconfig.json      # JavaScript 路径别名配置，优化导入路径
├── package.json
└── README.md
```
**项目结构优化注意：**
- 代码可维护性：按功能组织组件和资源，如 `components/`，`assets/` 子目录。
- 可扩展性：根据项目复杂度预留路由、状态管理等目录，如 `routes/`，`store/ `。
- 工程化规范：补充配置文件与测试目录，如 `__tests__`。
- 静态资源管理：明确 `public` 与 `src/assets` 的分工，如 `public/` 存放 favicon.ico 等必须通过绝对路径引用的文件，`src/assets/` 则存放需要通过 JavaScript 动态引用的资源（如图标、字体）。
- 全局样式管理：明确分工，如 `index.css` 仅用于全局基础样式，`App.css` 仅保留 `App.js` 的私有样式，避免污染其他组件。
- `.env` 文件放在根目录下，CRA 会自动加载，避免额外配置。
- 测试工具：如 Jest + React Testing Library。

## 三、常用命令

```bash
# 开发模式（含热更新）
npm start

# 生成生产环境构建包
npm run build

# 安装项目依赖
npm install package-name

# 检查代码规范
npm run lint

# 运行单元测试
npm test
```

## 四、开发建议

**1. 组件设计**
- 保持组件单一职责（SRP）：每个组件应只解决一个特定问题。
- 合理划分容器组件与展示组件
- 使用 `PropTypes` 进行类型检查：`npm install prop-types`

**2. ​调试技巧**
- 使用 `React Developer Tools` 浏览器插件
- 善用console调试组件生命周期
- 使用严格模式检测潜在问题

**3. ​性能优化**
- 使用 `React.memo` 缓存组件
- 使用 `useCallback` / `useMemo` 缓存函数和计算结果
- 代码分割：`React.lazy(() => import('./Component'))`

**4. 推荐工具链**
- 状态管理：`Zustand`（轻量）/ `Redux Toolkit`（企业级）
- UI库：`Material-UI` / `Ant Design`
- 构建工具：`Vite`（快速替代 `create-react-app`）