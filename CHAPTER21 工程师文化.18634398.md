# 为什么要倡导工程师文化
工程师文化，对于工程师而言，就是以正确的方式工作；而对于工程师以外的人而言，就是跟随工程师以正确的方式工作。

# 工程师文化的特征
- 创新有两个前提，一是在自由的环境下，二是对提高效率产生极致追求。
- 精神自由和效率正是工程师文化的核心特征。

## 精神自由
- 自我驱动：自我管理是最有效的管理，有兴趣才有持续的动力。
- 灵活的工作时间和地点：工程师从事的不是体力劳动，能自由安排工作时间和地点，可以使工程师的脑力劳动更有效。
- 信息平等：意见表达也需要平等，任何人都应该有发表意见和建议的机会，而不是看到问题不敢说出来，这样才能激发个人的思辨能力，从而产生更好的想法。
- 不害怕错误：面对错误时分析问题总结经验会鼓励进步，惩罚犯错会让人不敢冒险。
- 宽松的审批：审批带来的是对人的不信任，繁琐的流程和思维上的束缚，会遏制员工的创新思维和想象力。
- 20%的自由时间：员工可以用20%的自由时间做自己想做的项目。

## 效率
- 投资未来就不能仅关注眼前的成本，更重要的是，提高效率的文化能带来示范效应。
- 提升效率方向
    - 简化：简单意味着更容易被用户理解，也更易于维护和运维。
    - 坚决推行自动化：实现自动化需要大力开发用于持续集成、持续部署、自动化运维的工具。
    - 以高效决策为原则：包括采用扁平化管理、用自动化工具取代支撑工作、采用不超过10人的全栈小团队、不按技能而是按负责产品或功能进行人员分工、用开会来表决提案、通过产品的目标或信条来减少沟通和决策的过程。
    - 正确地对组件进行抽象：抽象是简化的一部分，意味着重用性、通用性和可扩展性。
    - 开发高质量的代码：高质量的代码不仅易于修改和维护，还可以减少程序员处理线上故障的时间，让团队可以做更多面向未来的创造性工作。
    - 不断提高标准并招聘最好的人：一个团队想要变得强大，就必须不断提高工作的质量标准，即要求团队要持续地培养和招聘更优秀的人。
    - 创建持续改善的文化：组织想借助不断反思来前进，少不了全体员工的参与。

# 工程师文化如何落地
- 内部推行文化可以选择的方式：
    - 在招聘、绩效考核和升职三方面下功夫。
    - 采用经济手段，让不做某件事的成本高于做的成本，这样更多人就会做出成本更低的选择。
- 工程师文化落地前提条件：
    - 团队要小：如果团队太大，个体很难体会切肤之痛，也就没有进步的动力。
    - 热爱学习：工程师验证并学习新的技术可以开阔眼界，引入并尝试新的思维方式能避免在原地打转。
    - 管理者要更相信技术：要对用技术解决问题这一思维充满信心，而不是依赖制度、流程和价值观。