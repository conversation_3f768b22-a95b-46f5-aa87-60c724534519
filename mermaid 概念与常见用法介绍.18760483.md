## 简介
Mermaid 是一款开源图表生成工具，通过 Markdown 式语法快速创建流程图、时序图、甘特图等十余种专业图表。其核心特性包括：  
- **代码化绘图**：用纯文本替代拖拽操作，支持 Git 版本管理  
- **跨平台渲染**：无缝嵌入 GitHub、Notion、VS Code 等开发协作工具  
- **AI 增强**：结合自然语言生成图表代码，降低使用门槛  

作为文档与系统的可视化桥梁，Mermaid 可显著提升技术方案设计、项目进度跟踪、系统架构描述等场景的效率，是开发者、技术写作者及项目管理者的高效工具。

---

## 核心功能
1. **多类型图表支持**  
   - **流程图（Flowchart）**：描述业务流程或算法逻辑，支持条件分支、循环结构。  
   - **序列图（Sequence Diagram）**：展示系统间交互时序，如 API 调用。  
   - **甘特图（Gantt Chart）**：规划项目时间线，定义任务依赖关系。  
   - **思维导图（Mindmap）**：梳理知识结构或创意发散。  
   - **架构图（C4 Diagram）**：可视化系统组件及通信协议。  
   - 其他：类图、状态图、看板图、用户旅程图等。

2. **核心优势**  
   - **纯文本编写**：代码化图表易于修改和版本管理。  
   - **跨平台兼容**：直接嵌入 Markdown、HTML、GitHub 等平台。  
   - **AI 协作**：结合 DeepSeek 等工具，可通过自然语言生成图表代码。  
   - **自定义样式**：支持 CSS 调整节点颜色、字体等。

---

## 应用场景
1. **技术文档**  
   - 绘制系统架构图、API 调用时序图，提升文档可读性。  
   - 示例：电商系统的容器图（C4 Diagram）展示服务间调用关系。  

2. **项目管理**  
   - 甘特图规划任务周期，看板图追踪敏捷开发进度。  
   - 示例：家庭假期计划中划分“准备阶段”与“旅行阶段”。  

3. **数据分析与教育**  
   - 思维导图整理知识点，数据流图展示处理流程。  
   - 示例：用流程图拆解早餐制作步骤。  

4. **团队协作**  
   - Git 分支图展示代码合并流程，状态图描述系统运行逻辑。  

---

## 基础用法

### 1. 基础语法
**声明图表类型**
以 graph TD（流程图）或 sequenceDiagram（序列图）开头。

**定义节点与连接**
```mermaid
flowchart LR  
A[开始] --> B{条件判断}  
B -->|是| C[执行操作]  
B -->|否| D[结束]  
```

**注释与布局**
使用 %% 添加注释，LR 或 TD 控制方向。

### 2. 工具使用
- 在线编辑器：通过 [Mermaid Live Editor](https://mermaid.live/) 实时渲染图表。
- 集成开发环境：VS Code 安装插件后可直接预览（如插件 Markdown Preview Mermaid Support）。
- AI 辅助生成：向工具（如 DeepSeek）描述需求，自动输出代码。

### 3. 常用图表

#### 3.1. **流程图（Flowchart）**  
**适用场景**：业务流程设计、算法逻辑描述、系统执行流程可视化。  
**用法示例**：  
```context
flowchart TD
    subgraph 用户模块[用户模块]
        A[用户登录] --> B{验证成功？}
    end
    B -->|是| C[进入主页]
    B -->|否| D[提示错误]
    C --> E[加载数据]
    D --> F[返回登录页]
```
```mermaid
flowchart TD
    subgraph 用户模块[用户模块]
        A[用户登录] --> B{验证成功？}
    end
    B -->|是| C[进入主页 ]
    B -->|否| D[提示错误]
    C --> E[加载数据]
    D --> F[返回登录页]
```  
**注意要点**：  
- **语法精准**：箭头符号（`-->`）、条件分支（`|是|`）需严格遵循语法，否则渲染失败。  
- **布局优化**：优先使用 `LR`（水平布局）或 `TD`（垂直布局），复杂流程可嵌套 `subgraph`。  
- **节点类型**：菱形表条件（`B{条件}`），矩形表操作（`A[登录]`）。

#### 3.2. **序列图（Sequence Diagram）**  
**适用场景**：系统间交互时序（如API调用）、用户与系统操作流程。  
**用法示例**：  
```context
sequenceDiagram
    autonumber
    participant 用户 as U
    participant 服务端 as S
    participant 数据库 as DB
    U->>S: 查询订单
    loop 每秒重试
        S->>DB: SELECT * FROM orders
        DB-->>S: 返回数据
    end
    S-->>U: 显示结果
    Note right of S: 超时自动终止
```
```mermaid
sequenceDiagram
    autonumber
    participant 用户 as U
    participant 服务端 as S
    participant 数据库 as DB
    U->>S: 查询订单
    loop 每秒重试
        S->>DB: SELECT * FROM orders
        DB-->>S: 返回数据
    end
    S-->>U: 显示结果
    Note right of S: 超时自动终止
```  
**注意要点**：  
- **参与者定义**：需明确 `participant` 名称，避免歧义。  
- **消息类型**：同步请求用 `->>`，异步响应用 `-->>`，循环块用 `loop`。  
- **自动编号**：`autonumber` 自动生成步骤序号。  
- **循环块**：`loop` 包裹重试逻辑，`Note` 添加右侧注释说明超时机制。  
- **参与者别名**：用 `as` 简化长名称（如 `用户 as U`）。

#### 3.3. **甘特图（Gantt Chart）**  
**适用场景**：项目管理、任务时间线规划、里程碑跟踪。  
**用法示例**：  
```context
gantt
    title 项目计划（紧凑模式）
    dateFormat  YYYY-MM-DD
    %%{init: {"gantt": {"displayMode": "compact"}}}%%
    section 设计
    需求分析       :crit, des1, 2025-03-10, 5d
    原型设计       :active, des2, after des1, 3d
    section 开发
    前端开发       :         des3, after des2, 7d
    后端联调       :         des4, after des3, 5d
```
```mermaid
gantt
    title 项目计划（紧凑模式）
    dateFormat  YYYY-MM-DD
    %%{init: {"gantt": {"displayMode": "compact"}}}%%
    section 设计
    需求分析       :crit, des1, 2025-03-10, 5d
    原型设计       :active, des2, after des1, 3d
    section 开发
    前端开发       :         des3, after des2, 7d
    后端联调       :         des4, after des3, 5d
```  
**注意要点**：  
- **时间格式**：`dateFormat` 需统一（如 `YYYY-MM-DD`）。  
- **关键路径**：标记 `crit` 突出关键任务。  
- **紧凑模式**：添加 `%%{init: {"gantt": {"displayMode": "compact"}}}` 简化显示。
- **依赖关系**：用 `after` 声明任务顺序。

#### 3.4. **类图（Class Diagram）**  
**适用场景**：面向对象系统设计、类关系与继承结构展示。  
**用法示例**：  
```context
classDiagram
    class 用户 {
        +String 用户名
        -String 密码
        +登录()
    }
    class 管理员 {
        +审批()
    }
    用户 "1" *-- "0..*" 订单 : 拥有
    用户 <|-- 管理员
```
```mermaid
classDiagram
    class 用户 {
        +String 用户名
        -String 密码
        +登录()
    }
    class 管理员 {
        +审批()
    }
    用户 "1" *-- "0..*" 订单 : 拥有
    用户 <|-- 管理员
```  
**注意要点**： 
- **关系符号**：继承用 `<|--`，组合用 `*--`，聚合用 `o--`，基数标记为 `"1" *-- "0..*"`。  
- **成员可见性**：`+` 表公有，`-` 表私有，`$` 表静态属性。  
- **类拆分**：复杂类可拆分至子图，避免代码冗长。
- **简化显示**：仅保留核心类，复杂属性拆分到子图。

#### 3.5. **状态图（State Diagram）**  
**适用场景**：对象状态转换（如电梯运行、订单状态）。  
**用法示例**：  
```context
stateDiagram-v2
    [*] --> 待机
    待机 --> 运行 : 启动按钮按下
    运行 --> 暂停 : 用户暂停
    暂停 --> 运行 : 继续
    运行 --> [*] : 关机
```
```mermaid
stateDiagram-v2
    [*] --> 待机
    待机 --> 运行 : 启动按钮按下
    运行 --> 暂停 : 用户暂停
    暂停 --> 运行 : 继续
    运行 --> [*] : 关机
```  
**注意要点**：  
- **状态命名**：使用动词短语（如 `处理中`）而非名词，避免歧义。  
- **转换条件**：箭头后添加 `: 条件` 说明触发事件。  
- **终止状态**：`[*]` 表示初始/终止状态。

#### 3.6. **思维导图（Mindmap）**  
**适用场景**：知识梳理、会议记录、创意发散。  
**用法示例**：  
```context
mindmap
root((产品需求))
    核心功能
        用户管理
            注册
            权限
        支付模块
    技术方案
        前端: React
        后端: Spring
```
```mermaid
mindmap
root((产品需求))
    核心功能
        用户管理
            注册
            权限
        支付模块
    技术方案
        前端: React
        后端: Spring
```  
**注意要点**：  
- **层级缩进**：用空格或制表符控制分支层级，建议缩进2-4空格。  
- **图标增强**：使用 Emoji（如 ✅）标记进度或优先级。  
- **中心主题**：`root` 需明确，分支避免超过4层。

#### 3.7. **实体关系图（ER Diagram）**  
**适用场景**：数据库设计、数据模型定义。  
**用法示例**：  
```context
erDiagram
CUSTOMER ||--o{ ORDER : "下单"
ORDER ||--|{ PRODUCT : "包含"
```
```mermaid
erDiagram
CUSTOMER ||--o{ ORDER : "下单"
ORDER ||--|{ PRODUCT : "包含"
```  
**注意要点**：  
- **关系基数**：`||--o{` 表一对多，`}|--||` 表多对多。  
- **属性定义**：实体字段需明确类型（如 `string title`）。  
- **避免冗余**：仅展示关键实体，复杂属性可拆分至子图。

#### 3.8. **用户旅程图（User Journey Diagram）**  
**适用场景**：用户体验设计、购物流程优化。  
**用法示例**：  
```context
journey
    title 购物流程优化
    section 浏览阶段
      用户: 搜索商品 :5
      系统: 推荐相关商品 :4
    section 支付阶段
      用户: 填写地址 :3 crit
      系统: 生成订单 :5
```
```mermaid
journey
    title 购物流程优化
    section 浏览阶段
      用户: 搜索商品 :5
      系统: 推荐相关商品 :4
    section 支付阶段
      用户: 填写地址 :3 crit
      系统: 生成订单 :5
```  
**注意要点**：  
- **阶段划分**：用 `section` 分隔流程节点（如浏览、支付）。  
- **情感标注**：添加 `:5` 评分（1-5分）标记用户满意度。  
- **痛点标记**：用 `crit` 高亮体验瓶颈（如支付失败）。
