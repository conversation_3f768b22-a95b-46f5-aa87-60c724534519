给定一个非空二叉树的根节点 root , 以数组的形式返回每一层节点的平均值。与实际答案相差 10-5 以内的答案可以被接受。

示例 1：
![image](https://img2024.cnblogs.com/blog/1548975/202412/1548975-20241223235412753-168458907.png)

> 输入：root = [3,9,20,null,null,15,7]
输出：[3.00000,14.50000,11.00000]
解释：第 0 层的平均值为 3,第 1 层的平均值为 14.5,第 2 层的平均值为 11 。
因此返回 [3, 14.5, 11] 。

示例 2:
![image](https://img2024.cnblogs.com/blog/1548975/202412/1548975-20241223235432623-770829276.png)

> 输入：root = [3,9,20,15,7]
输出：[3.00000,14.50000,11.00000]

提示：
- 树中节点数量在 [1, 104] 范围内
- -231 <= Node.val <= 231 - 1

go实现：
```go
/**
 * Definition for a binary tree node.
 * type TreeNode struct {
 *     Val int
 *     Left *TreeNode
 *     Right *TreeNode
 * }
 */
func averageOfLevels(root *TreeNode) []float64 {

	// 如果根节点为空时，直接返回空
	if root == nil {
		return []float64{}
	}

	// 定义返回结果
	result := []float64{}
	// 定义节点对象临时存储列表
	queue := []*TreeNode{root}

	// 当节点列表不为空时进行层级遍历
	for len(queue) > 0 {

		// 定义遍历层级，即节点列表长度
		levelSize := len(queue)
		// 定义节点值总和，格式显式指定为int64，防止32位环境导致数值溢出
		sum := int64(0)
		// 创建当前层的切片视图
		currentLevel := queue[:levelSize]
		// 提前分离当前层界面，避免直接操作动态变化的队列
		queue = queue[levelSize:]

		// 开始计算当前层节点累计和
		for _, node := range currentLevel {

			// 计算当前层节点累积和
			sum += int64(node.Val)

			// 如果当前节点左子节点不为空，则加入节点列表，以备下次遍历使用
			if node.Left != nil {
				queue = append(queue, node.Left)
			}

			// 如果当前节点右子节点如果不为空，则加入节点列表
			if node.Right != nil {
				queue = append(queue, node.Right)
			}
		}

		// 计算当前层节点平均值，并追加到结果列表
		avg := float64(sum) / float64(levelSize)
		result = append(result, avg)

	}

	return result
}
```