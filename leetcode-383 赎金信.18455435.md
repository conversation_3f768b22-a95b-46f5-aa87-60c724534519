给你两个字符串：ransomNote 和 magazine ，判断 ransomNote 能不能由 magazine 里面的字符构成。
如果可以，返回 true ；否则返回 false 。
magazine 中的每个字符只能在 ransomNote 中使用一次。

示例 1：
输入：ransomNote = "a", magazine = "b"
输出：false

示例 2：
输入：ransomNote = "aa", magazine = "ab"
输出：false

示例 3：
输入：ransomNote = "aa", magazine = "aab"
输出：true

提示：
1 <= ransomNote.length, magazine.length <= 105
ransomNote 和 magazine 由小写英文字母组成

实现：
```go
func canConstruct(ransomNote string, magazine string) bool {

    // 创建空字符映射
    char_map := make(map[string]int)

    // 遍历magazine，并将遍历到的每个字符在char_map中进行累计计数
    for _, mchar := range magazine {
        char_map[string(mchar)]++
    }

    // 遍历ransomnote，判断其字符在char_map中是否存在，如果存在则对应计数减1，如果不存在或计数为0，则返回false，否则返回true
    for _, rchar := range ransomNote {
        if char_map[string(rchar)] == 0 {
            return false
        }
        char_map[string(rchar)]--
    }

    return true
}
```