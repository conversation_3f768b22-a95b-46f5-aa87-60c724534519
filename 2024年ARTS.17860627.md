# ARTS打卡第53周（12.30-1.5）
> A: Algorithm 一道算法题

[leetcode-108 将有序数组转换为二叉搜索树](https://www.cnblogs.com/505donkey/p/18625328)
> R: Review 读一篇英文文章

[OpenAI brings ChatGPT to landlines OpenAI 将 ChatGPT 引入固定电话](https://www.cnblogs.com/505donkey/p/18634386)
> T: Technique/Tips 分享一个小技术

[--]()
> S: Share 分享一个观点

[CHAPTER21 工程师文化](https://www.cnblogs.com/505donkey/p/18634398)

# ARTS打卡第52周（12.23-12.29）
> A: Algorithm 一道算法题

[leetcode45 跳跃游戏 II](https://www.cnblogs.com/505donkey/p/18514755)
> R: Review 读一篇英文文章

[AI adds to fake reviews on internet: researchers 研究人员：AI增加了互联网上的虚假评论](https://www.cnblogs.com/505donkey/p/18634391)
> T: Technique/Tips 分享一个小技术

[--]()
> S: Share 分享一个观点

[CHAPTER20 关于招聘](https://www.cnblogs.com/505donkey/p/18234304)

# ARTS打卡第51周（12.16-12.22）
> A: Algorithm 一道算法题

[leetcode-151 反转字符串中的单词](https://www.cnblogs.com/505donkey/p/18514773)
> R: Review 读一篇英文文章

[The future of smartphones transformed by AI 人工智能改变智能手机未来](https://www.cnblogs.com/505donkey/p/18611937)
> T: Technique/Tips 分享一个小技术

[linux 安装pcre库（包含在线&离线安装）](https://www.cnblogs.com/505donkey/p/18600096)
> S: Share 分享一个观点

[松茸、竹子和蜜蜂：回归创新的常识](https://www.cnblogs.com/505donkey/p/18616091)

# ARTS打卡第50周（12.9-12.15）
> A: Algorithm 一道算法题

[leetcode-209 长度最小的子数组](https://www.cnblogs.com/505donkey/p/18581728)
> R: Review 读一篇英文文章

[40 new vocational majors introduced 新增40个职业专业](https://www.cnblogs.com/505donkey/p/18607434)
> T: Technique/Tips 分享一个小技术

[--]()
> S: Share 分享一个观点

[Part9 结语](https://www.cnblogs.com/505donkey/p/18570153)

# ARTS打卡第49周（12.2-12.8）
> A: Algorithm 一道算法题

[leetcode26 删除有序数组中的重复项](https://www.cnblogs.com/505donkey/p/18514744)
> R: Review 读一篇英文文章

[China's mobile phone shipments up 1.8% in October 10月份中国手机出货量增长1.8%](https://www.cnblogs.com/505donkey/p/18611933)
> T: Technique/Tips 分享一个小技术

[Linux服务器硬件配置检查](https://www.cnblogs.com/505donkey/p/18515860)：追加GPU部分内容
> S: Share 分享一个观点

[Part8 心力难题](https://www.cnblogs.com/505donkey/p/18534416)

# ARTS打卡第48周（11.25-12.1）
> A: Algorithm 一道算法题

[leetcode238 除自身以外数组的乘积](https://www.cnblogs.com/505donkey/p/18514760)
> R: Review 读一篇英文文章

[OmniParser for Pure Vision Based GUI Agent - Hugging face](https://www.cnblogs.com/505donkey/p/18554423)
> T: Technique/Tips 分享一个小技术

[【React】美团案例-添加购物车实现](https://www.cnblogs.com/505donkey/p/18545077)
> S: Share 分享一个观点

[Part7 出海难题](https://www.cnblogs.com/505donkey/p/18529247)

# ARTS打卡第47周（11.18-11.24）
> A: Algorithm 一道算法题

[leetcode134 加油站](https://www.cnblogs.com/505donkey/p/18514758)
> R: Review 读一篇英文文章

[OmniParser for Pure Vision Based GUI Agent - MicroSoft 基于纯视觉的客户端代理OmniParser - MicroSoft](https://www.cnblogs.com/505donkey/p/18554401)
> T: Technique/Tips 分享一个小技术

[【React】美团案例-商品列表切换显示](https://www.cnblogs.com/505donkey/p/18545010)
> S: Share 分享一个观点

[Part6 智能难题](https://www.cnblogs.com/505donkey/p/18527189)

# ARTS打卡第46周（11.11-11.17）
> A: Algorithm 一道算法题

[leetcode-14 最长公共前缀](https://www.cnblogs.com/505donkey/p/18514772)
> R: Review 读一篇英文文章

[I Asked ChatGPT How to Become a Millionaire as a Developer 我问chatgpt 作为一名开发者如何成为百万富翁](https://www.cnblogs.com/505donkey/p/17676185.html)
> T: Technique/Tips 分享一个小技术

[【React】美团案例-点击分类激活交互实现](https://www.cnblogs.com/505donkey/p/18457509)
> S: Share 分享一个观点

[Part5 变老难题](https://www.cnblogs.com/505donkey/p/18524933)

# ARTS打卡第45周（11.4-11.10）
> A: Algorithm 一道算法题

[leetcode-28 找出字符串中第一个匹配项的下标](https://www.cnblogs.com/505donkey/p/18514776)
> R: Review 读一篇英文文章

[AI offers fresh potential for group's cooperation AI为集团协作提供了新的潜力](https://www.cnblogs.com/505donkey/p/18492549)
> T: Technique/Tips 分享一个小技术

[zabbix6.4 容器离线部署](https://www.cnblogs.com/505donkey/p/18151787)
> S: Share 分享一个观点

[Part4 内卷难题](https://www.cnblogs.com/505donkey/p/18519412)

# ARTS打卡第44周（10.28-11.3）
> A: Algorithm 一道算法题

[leetcode-66 加一](https://www.cnblogs.com/505donkey/p/18492556)
> R: Review 读一篇英文文章

[AI creates more jobs but with higher entry threshold AI创造更多工作机会同时进入门槛也更高了](https://www.cnblogs.com/505donkey/p/18510551)
> T: Technique/Tips 分享一个小技术

[Linux服务器硬件配置检查](https://www.cnblogs.com/505donkey/p/18515860)
> S: Share 分享一个观点

[Part1 花总会开](https://www.cnblogs.com/505donkey/p/18512191)

# ARTS打卡第43周（10.21-10.27）
> A: Algorithm 一道算法题

[leetcode-69 x 的平方根](https://www.cnblogs.com/505donkey/p/18492562)
> R: Review 读一篇英文文章

[Duolingo English Test gains in popularity across China Duolingo 英语测试在中国越来越受欢迎](https://www.cnblogs.com/505donkey/p/18463669)
> T: Technique/Tips 分享一个小技术

[基于docker方式部署禅道](https://www.cnblogs.com/505donkey/p/18546196)
> S: Share 分享一个观点

[Part2 客户难题](https://www.cnblogs.com/505donkey/p/18515047)

# ARTS打卡第42周（10.14-10.20）
> A: Algorithm 一道算法题

[leetcode-136 只出现一次的数字](https://www.cnblogs.com/505donkey/p/18625337)
> R: Review 读一篇英文文章

[What We’re Reading: What makes you happy? 我们正在阅读：什么使你快乐？](https://www.cnblogs.com/505donkey/p/17688821.html)
> T: Technique/Tips 分享一个小技术

安全加固产品介绍与使用培训（内部）
> S: Share 分享一个观点

[Part3 价格难题](https://www.cnblogs.com/505donkey/p/18516976)

# ARTS打卡第41周（10.7-10.13）
> A: Algorithm 一道算法题

[leetcode-63 不同路径 II](https://www.cnblogs.com/505donkey/p/18459798)
> R: Review 读一篇英文文章

[Here's why computer scientists got Nobel for physics 这是计算机科学家获得诺贝尔物理学奖的原因](https://www.cnblogs.com/505donkey/p/18454686)
> T: Technique/Tips 分享一个小技术

[【React】美团案例-分类和商品列表渲染](https://www.cnblogs.com/505donkey/p/18425526)
> S: Share 分享一个观点

[【React】美团案例-环境准备](https://www.cnblogs.com/505donkey/p/18425458)

# ARTS打卡第40周（9.30-10.6）
> A: Algorithm 一道算法题

[leetcode-300 最长递增子序列](https://www.cnblogs.com/505donkey/p/18451958)
> R: Review 读一篇英文文章

[China's CPI up 0.3% in April 中国4月CPI上涨0.3%](https://www.cnblogs.com/505donkey/p/18188500)
> T: Technique/Tips 分享一个小技术

[【React】react-redux 提交action](https://www.cnblogs.com/505donkey/p/18417049)
> S: Share 分享一个观点

[【React】react-redux 异步状态操作](https://www.cnblogs.com/505donkey/p/18418981)

# ARTS打卡第39周（9.23-9.29）
> A: Algorithm 一道算法题

[64 最小路径和](https://www.cnblogs.com/505donkey/p/18455461)
> R: Review 读一篇英文文章

[Xi extends congratulations to Vietnam's new president X向越南新主席表示祝贺](https://www.cnblogs.com/505donkey/p/18207270)
> T: Technique/Tips 分享一个小技术

[【React】集中状态管理 Redux](https://www.cnblogs.com/505donkey/p/18400846)
> S: Share 分享一个观点

[【React】react-redux 实现Counter](https://www.cnblogs.com/505donkey/p/18409252)

# ARTS打卡第38周（9.16-9.22）
> A: Algorithm 一道算法题

[leetcode-120 三角形最小路径和](https://www.cnblogs.com/505donkey/p/18453924)
> R: Review 读一篇英文文章

[Grassroots civil service job no 'waste of talent' 基层公务员工作不“浪费人才”](https://www.cnblogs.com/505donkey/p/18259818)
> T: Technique/Tips 分享一个小技术

[【React】自定义hook函数](https://www.cnblogs.com/505donkey/p/18399483)
> S: Share 分享一个观点

[【React】redux 环境搭建](https://www.cnblogs.com/505donkey/p/18402709)

# ARTS打卡第37周（9.9-9.15）
> A: Algorithm 一道算法题

[leetcode-322 零钱兑换](https://www.cnblogs.com/505donkey/p/18454002)
> R: Review 读一篇英文文章

[Peng seeks stronger ties between Chinese, Senegalese people 彭寻求加强中国与塞内加尔人民之间的联系](https://www.cnblogs.com/505donkey/p/18398882)
> T: Technique/Tips 分享一个小技术

[【React】useEffect 清除副作用](https://www.cnblogs.com/505donkey/p/18396274)
> S: Share 分享一个观点

问题：硬盘裸盘性能测试时写数据可以达到240M/s，但通过rocksdb+bluefs写数据时速率却只有120M/s，同时磁盘利用率也达到了100%。

思考：
1、磁盘利用率达到100%，只能说明磁盘一直忙碌，但不能说明就一定高吞吐，即可能包含有低吞吐的IO操作，如随机IO。
2、描述里说到写数据时都是默认配置，那么可能有以下几方面会产生影响：
     - 压缩算法，rocksdb在达到一定文件数量后触发分层压缩机制，分L0-L6，这块是系统根据配置自行计算的，默认压缩算法是snappy，触发分层压缩后会消耗CPU，这点会影响IO性能；
     - 写入模式，rocksdb默认是异步写，这时会先写内存，即memtable，然后达到一定数据量后就开始flush了，即落盘，这时也开启了分层压缩，然后落盘速度肯定会比写内存慢，主要写内存时是偏顺序写，但落盘时是偏随机写，另外压缩过程也会产生随机IO，进而影响写性能；
     - 数据结构，rocksdb是键值结构数据，如果写入key比较随机也会产生大量随机IO，这也会影响写入性能；
3、fio测试主要是反映磁盘在特定条件下的峰值性能，而rocksdb的写入性能则受到多方面因素的影响，通常是无法达到fio测试的性能水平的，只能作为基准参考。

综合以上分析，如果只关注写性能，可以尝试关闭压缩来降低CPU开销对IO的影响，然后代价是空间这块要有一定的让步，另外像缓存区大小的设置，压缩策略的选择，写入key的顺序性等都可能影响写性能，所以也要一起综合调校，去验证rocksdb的IO性能表现。

# ARTS打卡第36周（9.2-9.8）
> A: Algorithm 一道算法题

[leetcode-198 打家劫舍](https://www.cnblogs.com/505donkey/p/18455458)
> R: Review 读一篇英文文章

[China ahead in humanoid robots field 中国在仿人机器人领域领先](https://www.cnblogs.com/505donkey/p/18393986)
> T: Technique/Tips 分享一个小技术

[【React】useEffect 依赖项](https://www.cnblogs.com/505donkey/p/18395119)
> S: Share 分享一个观点

有个说法，新技术应用得最快的，往往是军事，诈骗和色情，所以今天出现这个事情（韩国使用deepface换脸再现N号房），某种程度上也是有一定心理预期的。

但这样说，并不意味着就两手一摊放之任之，虽然他国的事情我们无法干预，但这问题背后所引发的社会影响与反思，却是我们要共同面对的，即便无法根本杜绝，但也不意味着我们不能做一些事情。

比如在技术层面，技术发展过程带来的衍生问题，需要、也可以通过技术发展来制衡，方符谓之进步之名；在管理层面，加强对社会主流价值的引导，通过技术+行政手段提升违法犯罪成本，压制恶之花生长的土壤，网络不是法外之地。

比较认同一个观念，人群自有左中右，既有使用AI作恶的人，就有使用AI行善的人，人的内心本能就向往积极正面的能量，虽然阴影无法消除，却可以选择面对阳光。

# ARTS打卡第35周（8.26-9.1）
> A: Algorithm 一道算法题

[leetcode-20 有效的括号](https://www.cnblogs.com/505donkey/p/18455454)
> R: Review 读一篇英文文章

[AI-powered system assists Hebei nature reserve in protecting birds AI智能系统助力河北自然保护区鸟类保护](https://www.cnblogs.com/505donkey/p/18380085)
> T: Technique/Tips 分享一个小技术

[【React】样式控制](https://www.cnblogs.com/505donkey/p/18382425)
> S: Share 分享一个观点

未来干掉你的不是AI，而是比你会用AI的人，怎么理解？

① AI工具的迭代，会不断降低工具的使用门槛，而大部分人都能使用的工具能力，难以形成个体间的竞争优势，所以对AI工具能力的应用，只是会用AI的基础；
② AI内置的很多专业领域内的知识，显著降低了很多跨领域应用的门槛，又因为其基于概率推理的特性，用户自身如果没有一定专业领域内的基础，将无法有效保证AI工具的产出质量，所以具备一定的专业基础，是用好AI的前提；
③ 不同的AI工具对应不同的能力，对期望产出要求越高，背后涉及的能力维度与协调往往就越复杂，所以如何管理好不同能力的AI工具，是用好AI的重要保证；

# ARTS打卡第34周（8.19-8.25）
> A: Algorithm 一道算法题

[leetcode-228 汇总区间](https://www.cnblogs.com/505donkey/p/18455451)
> R: Review 读一篇英文文章

[Race for AI talent heats up amid demand 对于AI人才的争夺战在需求中升温](https://www.cnblogs.com/505donkey/p/18368435)
> T: Technique/Tips 分享一个小技术

[【React】标准库useState](https://www.cnblogs.com/505donkey/p/18378828)
> S: Share 分享一个观点

[【React】组件介绍](https://www.cnblogs.com/505donkey/p/18378776)
# ARTS打卡第33周（8.12-8.18）
> A: Algorithm 一道算法题

[leetcode-70 爬楼梯](https://www.cnblogs.com/505donkey/p/18455456)
> R: Review 读一篇英文文章

[Japanese veteran testifies to Unit 731 crimes, apologizes 日本老兵为731部队罪行作证并道歉](https://www.cnblogs.com/505donkey/p/18358176)
> T: Technique/Tips 分享一个小技术

[自然语言处理](https://www.cnblogs.com/505donkey/p/18363717)
> S: Share 分享一个观点

历史上，电力在引入工厂之后并不比蒸汽机创造了更多的生产力，是过了大概30年左右，分布式电源改造了车间布局，推动组装系统的出现，再才开始了生产力的飞跃。现在的AI和当初的电力一样，有价值，但还需要组织创新，才能真正拿到巨大的回报，目前大家都还只是在摘取 ”低垂的果实“。

# ARTS打卡第32周（8.5-8.11）
> A: Algorithm 一道算法题

[leetcode-139 单词拆分](https://www.cnblogs.com/505donkey/p/18455460)
> R: Review 读一篇英文文章

[Artifacts are now generally available 人工制品现在已经普遍可用](https://www.cnblogs.com/505donkey/p/18387942)
> T: Technique/Tips 分享一个小技术

[【React】事件绑定](https://www.cnblogs.com/505donkey/p/18378064)
> S: Share 分享一个观点

[【React】JSX](https://www.cnblogs.com/505donkey/p/18374840)

# ARTS打卡第31周（7.29-8.4）
> A: Algorithm 一道算法题

[leetcode-191 位1的个数](https://www.cnblogs.com/505donkey/p/18625336)
> R: Review 读一篇英文文章

[American students experience China up close 美国学生近距离体验中国](https://www.cnblogs.com/505donkey/p/18340773)
> T: Technique/Tips 分享一个小技术

[【React】开发环境搭建](https://www.cnblogs.com/505donkey/p/18374808)
> S: Share 分享一个观点

[【React】框架简介](https://www.cnblogs.com/505donkey/p/18374567)

# ARTS打卡第30周（7.22-7.28）
> A: Algorithm 一道算法题

[leetcode-35 搜索插入位置](https://www.cnblogs.com/505donkey/p/18625330)
> R: Review 读一篇英文文章

[Maiden voyage: Thailand to Laos train adventure 处女航：泰国至老挝的火车之旅](https://www.cnblogs.com/505donkey/p/18314379)
> T: Technique/Tips 分享一个小技术

[【React】三方库classnames](https://www.cnblogs.com/505donkey/p/18389367)
> S: Share 分享一个观点

刚开始妙鸭相机的爆火，其实是吃了技术的红利，因为确实效果不比那些线下实体写真差，顶个六七成，当然了体验过也有不少瑕疵：表情不自然，线上排队久，实际体验效果不佳等，但之所以称之为红利，是因为开始玩家少，然后当大家看到有利可图纷纷下场时，红利就逐渐消失了。

虽然一开始吸引来了关注，但因为没有持续维持住开始给到用户的这种新鲜感，一个是技术门槛上，一个是用户需求洞察上，等新鲜劲一过，热度就迅速褪去了。

为什么都在呼吁要坚持难而正确的事，就是不希望付诸心血的产品生命力只是昙花一现。当然了，产品本身不是核心竞争力，产品背后的人才是。商海博弈，浮沉本是常态，失意的背后，可能就孕育着下一个耀眼的崛起。

# ARTS打卡第29周（7.15-7.21）
> A: Algorithm 一道算法题

[leetcode-202 快乐数](https://www.cnblogs.com/505donkey/p/18455448)
> R: Review 读一篇英文文章

[AI used to help preserve China's oldest wooden pagoda 人工智能被用来帮助保护中国最古老的木塔](https://www.cnblogs.com/505donkey/p/18304352)
> T: Technique/Tips 分享一个小技术

[Firewalld 服务配置](https://www.cnblogs.com/505donkey/p/18300231)
> S: Share 分享一个观点

有时面对一些困难，可能存在诸多限制因素限制了困难的解决，但有时解决之道可能就藏在这诸多的限制当中，而能发现解决问题的关键，一方面来自于问题未解决带来的压力和紧迫感，一方面也需要能打开思路，寻找多种解决问题的方式，不要被当前现状所限制。

# ARTS打卡第28周（7.8-7.14）
> A: Algorithm 一道算法题

[leetcode-1 两数之和](https://www.cnblogs.com/505donkey/p/18455444)
> R: Review 读一篇英文文章

[Drainage work begins after dike breach sealed 提防决口后封堵工作开启](https://www.cnblogs.com/505donkey/p/18293566)
> T: Technique/Tips 分享一个小技术

[Firewalld 介绍](https://www.cnblogs.com/505donkey/p/18297455)
> S: Share 分享一个观点

低谷时期，闭嘴，修炼，清零。

# ARTS打卡第27周（7.1-7.7）
> A: Algorithm 一道算法题

[leetcode-242 有效的字母异位词](https://www.cnblogs.com/505donkey/p/18455442)
> R: Review 读一篇英文文章

[World in focus: Life goes on](https://www.cnblogs.com/505donkey/p/18278618)
> T: Technique/Tips 分享一个小技术

[Centos终端输出时中文显示乱码](https://www.cnblogs.com/505donkey/p/18276864)
> S: Share 分享一个观点

技术带来的问题，最终会由技术自己去解决。

# ARTS打卡第26周（6.24-6.30）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[Shenzhen-Zhongshan link in S China's Guangdong to open to traffic 广东深圳至中山线路开放通车](https://www.cnblogs.com/505donkey/p/18276587)
> T: Technique/Tips 分享一个小技术

[shell判断系统路径中是否存在空格](https://www.cnblogs.com/505donkey/p/18289208)
> S: Share 分享一个观点

追求更优解是一种态度，是一种思维习惯，只有用最高的标准要求自己和自己的工作，才可能渐渐走向卓越，而不注重细节会让人在不知不觉间滑向平庸。

# ARTS打卡第25周（6.17-6.23）
> A: Algorithm 一道算法题

[leetcode-205 同构字符串](https://www.cnblogs.com/505donkey/p/18455436)
> R: Review 读一篇英文文章

[China-Japan relations 'at critical stage': FM 中日关系“正处于关键阶段”: FM](https://www.cnblogs.com/505donkey/p/18387898)
> T: Technique/Tips 分享一个小技术

[【React】受控表单绑定](https://www.cnblogs.com/505donkey/p/18389558)
> S: Share 分享一个观点

都在做事，但有的人是为了活下去，有的人是为了活的更好，有的人是为了帮助别人活的更好。做事的目的取决于每个人不同的人生阶段和生存情况，也取决于各自的精神寄托和理想信念。

# ARTS打卡第24周（6.10-6.16）
> A: Algorithm 一道算法题

[leetcode-383 赎金信](https://www.cnblogs.com/505donkey/p/18455435)
> R: Review 读一篇英文文章

[Reality show wraps up with touching family bonds](https://www.cnblogs.com/505donkey/p/18246829)
> T: Technique/Tips 分享一个小技术

[fanotify介绍](https://www.cnblogs.com/505donkey/p/18250217)
> S: Share 分享一个观点

创新就源于在没有路的地方自己开路。

# ARTS打卡第23周（6.03-6.09）
> A: Algorithm 一道算法题

[leetcode-392 判断子序列](https://www.cnblogs.com/505donkey/p/18455433)
> R: Review 读一篇英文文章

[Shield tunneling machine launched in Guangdong province 盾构挖掘机在广东省投入使用](https://www.cnblogs.com/505donkey/p/18225527)
> T: Technique/Tips 分享一个小技术

[centos7 安装 <strong>mysql</strong>5.7](https://www.cnblogs.com/505donkey/p/17945759)
新增离线部署方式
> S: Share 分享一个观点

应聘者只有在坦诚、自然的状态下才能暴露真实的一面；重要的不是知识，而是获取知识的能力；要关注的不是问题的答案，而是解题的思路和方法。

# ARTS打卡第22周（5.27-6.02）
> A: Algorithm 一道算法题

[leetcode-125 验证回文串](https://www.cnblogs.com/505donkey/p/18455430)
> R: Review 读一篇英文文章

[Xiaomi to roll out its first EV on March 28 小米将在3月28号推出首款电动汽车](https://www.cnblogs.com/505donkey/p/18069923)
> T: Technique/Tips 分享一个小技术

[记一次登录提示bash错误处理](https://www.cnblogs.com/505donkey/p/18237422)
> S: Share 分享一个观点

公司本质是一个商业载体，就像一艘船，所以在公司里找所谓的安全感或归属感，终究只能是个假象，当这艘船风雨飘摇甚至摇摇欲坠时，所有人都只能弃船，所以身在职场，及早看透这一表象并做好随时下船的准备，才有可能把握自己人生的主动权。

# ARTS打卡第21周（5.20-5.26）
> A: Algorithm 一道算法题

[leetcode-9 回文数](https://www.cnblogs.com/505donkey/p/18492555)
> R: Review 读一篇英文文章

[6G to be commercially available by about 2030: experts 6G将于2030年左右投入商业使用：专家](https://www.cnblogs.com/505donkey/p/18164620)
> T: Technique/Tips 分享一个小技术

[【React】获取DOM](https://www.cnblogs.com/505donkey/p/18389657)
> S: Share 分享一个观点

[【React】三方库uuid](https://www.cnblogs.com/505donkey/p/18391133)

# ARTS打卡第20周（5.13-5.19）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[--]()
> T: Technique/Tips 分享一个小技术

[docker-compose离线部署](https://www.cnblogs.com/505donkey/p/18197011)
> S: Share 分享一个观点

[CHAPTER19 绩效考核](https://www.cnblogs.com/505donkey/p/18184153)

# ARTS打卡第19周（5.06-5.12）
> A: Algorithm 一道算法题

[leetcode-12 整数转罗马数字](https://www.cnblogs.com/505donkey/p/18455426)
> R: Review 读一篇英文文章

[著名作家故居吸引大批游客](https://www.cnblogs.com/505donkey/p/18185345)
> T: Technique/Tips 分享一个小技术

[centos离线安装docker](https://www.cnblogs.com/505donkey/p/18184260)
> S: Share 分享一个观点

[CHAPTER18 管理方式](https://www.cnblogs.com/505donkey/p/18098499)

# ARTS打卡第18周（4.29-5.05）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[--]()
> T: Technique/Tips 分享一个小技术

[【React】三方库uuid](https://www.cnblogs.com/505donkey/p/18391133)
> S: Share 分享一个观点

[CHAPTER17 技术领导力](https://www.cnblogs.com/505donkey/p/18063591)

# ARTS打卡第17周（4.22-4.28）
> A: Algorithm 一道算法题

[leetcode-80 删除有序数组中的重复项 II](https://www.cnblogs.com/505donkey/p/18455403)
> R: Review 读一篇英文文章

[神州十六号船员获表彰](https://www.cnblogs.com/505donkey/p/18146844)
> T: Technique/Tips 分享一个小技术

[centos离线安装docker，docker-compose](https://www.cnblogs.com/505donkey/articles/18151705)
> S: Share 分享一个观点

[CHAPTER16 研发效率](https://www.cnblogs.com/505donkey/p/18053287)

# ARTS打卡第16周（4.15-4.21）
> A: Algorithm 一道算法题

[leetcode-188 买卖股票的最佳时机 IV](https://www.cnblogs.com/505donkey/p/18454014)
> R: Review 读一篇英文文章

[高盛上调2024年中国增长前景](https://www.cnblogs.com/505donkey/p/18133609)
> T: Technique/Tips 分享一个小技术

[Centos7部署与配置KVM服务](https://www.cnblogs.com/505donkey/articles/18123246)
> S: Share 分享一个观点

[CHAPTER15 时间管理](https://www.cnblogs.com/505donkey/p/18048125)

# ARTS打卡第15周（4.8-4.14）
> A: Algorithm 一道算法题

[leetcode-123 买卖股票的最佳时机 III](https://www.cnblogs.com/505donkey/p/18454007)
> R: Review 读一篇英文文章

[咖啡文化使参与者收益](https://www.cnblogs.com/505donkey/p/18107993)
> T: Technique/Tips 分享一个小技术

[Ansible基于centos7安装部署](https://www.cnblogs.com/505donkey/articles/17833144.html)
> S: Share 分享一个观点

[CHAPTER14 分布式架构](https://www.cnblogs.com/505donkey/p/18034310)

# ARTS打卡第14周（4.1-4.7）
> A: Algorithm 一道算法题

[leetcode-122 买卖股票的最佳时机 II](https://www.cnblogs.com/505donkey/p/18455415)
> R: Review 读一篇英文文章

[宁夏新居民身份证助力外国人](https://www.cnblogs.com/505donkey/p/18094435)
> T: Technique/Tips 分享一个小技术

[NetworkManager之设备管理](https://www.cnblogs.com/505donkey/articles/18094453)
> S: Share 分享一个观点

[CHAPTER13 软件开发与架构设计的原则](https://www.cnblogs.com/505donkey/p/18034309)

# ARTS打卡第13周（3.25-3.31）
> A: Algorithm 一道算法题

[leetcode-121 买卖股票的最佳时机](https://www.cnblogs.com/505donkey/p/18454023)
> R: Review 读一篇英文文章

[--]()
> T: Technique/Tips 分享一个小技术

[NetworkManager之连接管理](https://www.cnblogs.com/505donkey/articles/18082084)
> S: Share 分享一个观点

[CHAPTER12 编程范式](https://www.cnblogs.com/505donkey/p/18034303)
# ARTS打卡第12周（3.18-3.24）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[--]()
> T: Technique/Tips 分享一个小技术

[【React】三方库day.js](https://www.cnblogs.com/505donkey/p/18391165)
> S: Share 分享一个观点

[CHAPTER11 优质代码](https://www.cnblogs.com/505donkey/p/18034301)

# ARTS打卡第11周（3.11-3.17）
> A: Algorithm 一道算法题

[leetcode-169 多数元素](https://www.cnblogs.com/505donkey/p/18514747)
> R: Review 读一篇英文文章

[Targets set in 2024 Government Work Report](https://www.cnblogs.com/505donkey/p/18065329)
> T: Technique/Tips 分享一个小技术

[NetworkManager介绍与使用](https://www.cnblogs.com/505donkey/articles/17996769)
> S: Share 分享一个观点

[CHAPTER10 编程的本质](https://www.cnblogs.com/505donkey/p/18034296)

# ARTS打卡第10周（3.4-3.10）
> A: Algorithm 一道算法题

[leetcode-13 罗马数字转整数](https://www.cnblogs.com/505donkey/p/18514770)
> R: Review 读一篇英文文章

[China's foreign trade up 8.7% in Jan-Feb period](https://www.cnblogs.com/505donkey/p/18060090)
> T: Technique/Tips 分享一个小技术

[linux iptables介绍与常见用法](https://www.cnblogs.com/505donkey/articles/18034832)
> S: Share 分享一个观点

[CHAPTER9 高效沟通 - 如何应对沟通障碍](https://www.cnblogs.com/505donkey/p/18034290)

# ARTS打卡第9周（2.26-3.3）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[Microsoft buys nearly 500,000 Nvidia flagship chips in 2024 2024年微软购买了英伟达近50万块旗舰芯片](https://www.cnblogs.com/505donkey/p/18634390)
> T: Technique/Tips 分享一个小技术

[【React】组件通信 - 父传子](https://www.cnblogs.com/505donkey/p/18391183)
> S: Share 分享一个观点

[CHAPTER8 高效学习 - 学习是为了了解自己](https://www.cnblogs.com/505donkey/p/18034286)

# ARTS打卡第8周（2.19-2.25）
> A: Algorithm 一道算法题

[leetcode-88 合并两个有序数组](https://www.cnblogs.com/505donkey/p/18514736)
> R: Review 读一篇英文文章

[China's first narrow body aircraft to make debut in Singapore](https://www.cnblogs.com/505donkey/p/18022236)
> T: Technique/Tips 分享一个小技术

[【React】组件通信 - 子传父](https://www.cnblogs.com/505donkey/p/18392020)
> S: Share 分享一个观点

[CHAPTER7 程序员的修炼之道](https://www.cnblogs.com/505donkey/p/18028474)

# ARTS打卡第7周（2.12-2.18）
> A: Algorithm 一道算法题

[leetcode-27 移除元素](https://www.cnblogs.com/505donkey/p/18514740)
> R: Review 读一篇英文文章

[China's SMEs active in patent innovation: CNIPA 中国中小企业积极参与专利创新: CNIPA](https://www.cnblogs.com/505donkey/p/18634395)
> T: Technique/Tips 分享一个小技术

[【React】组件通信 - 兄弟通信](https://www.cnblogs.com/505donkey/p/18392024)
> S: Share 分享一个观点

[CHAPTER6 成长中的问题](https://www.cnblogs.com/505donkey/p/18028472)

# ARTS打卡第6周（2.5-2.11）
> A: Algorithm 一道算法题

[leetcode26 删除有序数组中的重复项](https://www.cnblogs.com/505donkey/p/18514744)
> R: Review 读一篇英文文章

[China's National Winter Games officially opens](https://www.cnblogs.com/505donkey/p/18019103)
> T: Technique/Tips 分享一个小技术

[【React】组件通信 - 跨层通信](https://www.cnblogs.com/505donkey/p/18392025)
> S: Share 分享一个观点

[CHAPTER5 有竞争力的程序员](https://www.cnblogs.com/505donkey/p/18028470)

# ARTS打卡第5周（1.29-2.4）
> A: Algorithm 一道算法题

[leetcode-42 接雨水](https://www.cnblogs.com/505donkey/p/18454019)
> R: Review 读一篇英文文章

[Hopes expressed for collaboration on AI](https://www.cnblogs.com/505donkey/p/17996732)
> T: Technique/Tips 分享一个小技术

[mysql5.7生成ssl证书](https://www.cnblogs.com/505donkey/articles/17983120)
> S: Share 分享一个观点

[CHAPTER4 做正确的事，等着被“开除”](https://www.cnblogs.com/505donkey/p/18028468)

# ARTS打卡第4周（1.22-1.28）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[AI will continue to make waves in 2024-P2](https://www.cnblogs.com/505donkey/p/17966701)
> T: Technique/Tips 分享一个小技术

[openEuler22.03安装GNOME桌面环境](https://www.cnblogs.com/505donkey/articles/17980017)
> S: Share 分享一个观点

[CHAPTER3 中年危机](https://www.cnblogs.com/505donkey/p/18028465)

# ARTS打卡第3周（1.15-1.21）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[AI will continue to make waves in 2024-P1](https://www.cnblogs.com/505donkey/p/17966701)
> T: Technique/Tips 分享一个小技术

[openEuler22.03安装tigervnc](https://www.cnblogs.com/505donkey/articles/17980140)
> S: Share 分享一个观点

[CHAPTER2 我对技术的态度 - 如何保持对技术的热情](https://www.cnblogs.com/505donkey/p/18028458)

# ARTS打卡第2周（1.8-1.14）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[Domestic oil, gas production hits record in 2023](https://www.cnblogs.com/505donkey/p/17955845)
> T: Technique/Tips 分享一个小技术

[centos7.9 安装 mysql8.0](https://www.cnblogs.com/505donkey/articles/17954029)
> S: Share 分享一个观点

[CHAPTER1 我的三观 - 价值取向](https://www.cnblogs.com/505donkey/p/18028451)

# ARTS打卡第1周（1.1-1.7）
> A: Algorithm 一道算法题

[--]()
> R: Review 读一篇英文文章

[Cities extend metro services for New Year](https://www.cnblogs.com/505donkey/p/17939458)
> T: Technique/Tips 分享一个小技术

[修改账户密码时提示 ERROR 1290 (HY000): The MySQL server is running with the --skip-grant-tables option so it cannot execute this statement 错误](https://www.cnblogs.com/505donkey/p/17945668)
> S: Share 分享一个观点

斯托克代尔悖论核心观点：对前途充满信心，但又直面残酷的现实。即要坚持你一定会成功的信念，同时，要面对现实中最残酷的事实，不论有多大的困难，不论它们是什么。放在现实生活中，那些过于乐观，寄希望于容易的措施和简单的变化能把情况变好的人，往往不能坚持到最后。并在接二连三的失望中逐渐丧失了信心。相反，那些能够坚持到最后的人，都是那些能够直面现实，同时又对未来充满信心的人。