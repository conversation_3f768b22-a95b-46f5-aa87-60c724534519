编写一个函数来查找字符串数组中的最长公共前缀。

如果不存在公共前缀，返回空字符串 ""。

示例 1：
> 输入：strs = ["flower","flow","flight"]
输出："fl"

示例 2：
> 输入：strs = ["dog","racecar","car"]
输出：""
解释：输入不存在公共前缀。

提示：
- 1 <= strs.length <= 200
- 0 <= strs[i].length <= 200
- strs[i] 仅由小写英文字母组成

Go实现：
```go
func longestCommonPrefix(strs []string) string {

	// 输入数组判空检查，为空则返回空字符串
	n := len(strs)
	if n == 0 {
		return ""
	}

	// 初始化数组第一个字符串tmpStr作为基准进行后续比较，同时定义并初始化返回结果publicPrefix
	tmpStr, publicPrefix := strs[0], ""

	// 外层循环检查基准字符串每个字符，内层循环检查当前比较字符和其他数组元素同个位置字符，如果与当前字符不相同或是在其他数组元素中不存在，则直接返回
	for i := 0; i < len(tmpStr); i++ {
		for j := 0; j < n; j++ {
			if i >= len(strs[j]) || tmpStr[i] != strs[j][i] {
				return publicPrefix
			}
		}
		// 更新公共前缀
		publicPrefix += string(tmpStr[i])
	}

	// 循环结束后返回结果
	return publicPrefix
}
```