给定一个只包括 '('，')'，'{'，'}'，'['，']' 的字符串 s ，判断字符串是否有效。

有效字符串需满足：
左括号必须用相同类型的右括号闭合。
左括号必须以正确的顺序闭合。
每个右括号都有一个对应的相同类型的左括号。

示例 1：
输入：s = "()"
输出：true

示例 2：
输入：s = "()[]{}"
输出：true

示例 3：
输入：s = "(]"
输出：false

示例 4：
输入：s = "([])"
输出：true

提示：
1 <= s.length <= 104
s 仅由括号 '()[]{}' 组成

实现：
```go
func isValid(s string) bool {

    // 定义类栈数据结构，注意字符使用rune数据结构
    stack := []rune{}

    // 定义括号匹配关系
    pairngs := map[rune]rune{
        '(': ')',
        '[': ']',
        '{': '}',
    }

    // 开始遍历
    for _, char := range s {

        // 如果当前字符为左括号，则压入栈中
        if _, found := pairngs[char]; found {
            stack = append(stack, char)
        // 如果当前字符为右括号，则检查栈长度是否为0或栈顶元素是否与当前字符不匹配，是则返回错误，反之则弹出栈顶元素
        } else {
            if len(stack) == 0 || pairngs[stack[len(stack)-1]] != char {
                return false
            }
            stack = stack[:len(stack)-1]
        }
    }

    // 结束遍历后，检查栈是否已清零，是则正确，反之则错误
    return len(stack) == 0

}
```