# Counter实现

## 思路

## 实现

### 使用 react toolkit 创建 counterStore

#### counterStore.js
- 
```js
import { createSlice } from "@reduxjs/toolkit"

const counterStore = createSlice({
    name: 'counter',
    // 初始化state
    initialState: {
        count: 0
    },
    // 修改静态的方法 同步方法 支持直接修改
    reducers: {
        increment(state) {
            state.count++
        },
        decrement(state) {
            state.count--
        }
    }
})

// 解构出来 actionCreater 函数
const { increment, decrement } = counterStore.actions
// 获取reducer
const reducer = counterStore.reducer

// 以按需导出的方式导出 actionCreater
export { increment, decrement }
// 以默认导出的方式导出 reducer
export default reducer 
```

#### store/index.js
- 
```js
import { configureStore } from "@reduxjs/toolkit"
// 导入子模块reducer
import counterReducer from "./modules/counterStore"

const store = configureStore({
    reducer: {
        counter: counterReducer
    }
})

export default store
```

### 为 react 注入 store
- react-redux 负责把react和redux链接起来，内置provider组件，通过store参数把创建好的store实例注入到应用中，链接正式成立。

#### 示例 index.js
- 
```js
...
import store from './store'
import { Provider } from 'react-redux'

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
); 
```

### react组件中使用store数据
- 在react组件中使用store中的数据，需要用到一个钩子函数 useSeletor，它的作用是把store中的数据映射到组件中。

#### 示例 App.js
- 
```js
import { useSelector } from 'react-redux'

function App() {
  const { count } = useSelector(state => state.counter)
  return (
    <div className="App">
      {count}
    </div>
  );
}

export default App;
```