# 自然语言定义
- 让机器能够用人类熟悉的语言自然地进行沟通的技术，背后用到的就是深度学习的技术。
- 自然语言，就是人类在自然而然地情况下，发展出来互相沟通的语言，也是相对于另外一种人造的语言而言（比如python，java）。
- 自然语言有两种形态，一种是写的，以文字状态呈现；一种是说的，以语音状态呈现。
- 一般讲自然语言处理，更多关注对文字的处理，但忽略对语音的部分，而实际自然语言应该是包含文字与语音，且语音部分在实际应用中占比更大，所以适量地体现文字和语音，才是广义的自然语言处理。
- 据人类民族网统计，世界上只有56%的语言有文字，即有接近一半的语言没有文字。同时，有文字的语言里面，多数人可能都不会使用自己语言文字的书写系统，所以要让机器了解语言，语音是无法忽视的部分。

# 人类语言处理六种模型
- 输入是语音，输出是文字
	- 语音辨识ASR
- 输入是文字，输出是语音
	- 语音合成
- 输入是语音，输出是语音
	- 语音分离，voice conversion（语音转换）
- 输入是语音，输出是语音类别
	- speaker recognition，keyword spotting（如wake up words 唤醒词应用）
- 输入是文字，输出是文字
	- autoregressive（从左往右）
	- non-autoregressive（关键词汇）
    - summarization 如文章自动摘要
    - Chatbot 聊天机器人
    - translation 如翻译
    - question answering 
    - syntactic parsing 语法解析
- 输入是文字，输出是文字类别

# meta learing 让机器学习如何学习
- 今天的学习算法是人设计出来的，这样设计出来的算法，会受到人能力的限制。
- meta learing 是先让机器在很多的任务上进行学习，通过归纳出更好的学习方法，成为更厉害的学习者，进而用更少的资料，更短的时间就能学会任务。
- mata learning 在语音文字上的应用
    - image style transfer 图像风格转换
    - voice conversion 语音风格转换
    - summarization 文件摘要
    - unsupervised translation 无监督翻译
    - speech recognition 语音辨识
    - knowledge graph 知识图谱（把学到的知识放在模型里）

# Adversarial Attack
- anti-spoofing system
    - 用来侦测一段语音信号是否被编造，是否为语音转换，或语音合成，或录音
    - 同时该系统也可以通过添加微小的杂讯进行语音攻击，绕过侦测
    - 语音识别也很容易被攻击
- 文字
    - 通过在文字材料上添加干扰，达到攻击文字的目的

# Explainable AI