## 简介
`yarn` 和 `npm` 都是 JavaScript 生态中常用的包管理工具，用于安装、管理和发布 JavaScript 包。它们的功能类似，但在性能、特性和使用体验上有一些区别。

## 背景

| **工具** | **背景**                                                                                                  |
| -------- | --------------------------------------------------------------------------------------------------------- |
| **npm**  | Node.js 自带的包管理工具，历史悠久，生态庞大。<br>是 JavaScript 生态的默认包管理工具。                    |
| **yarn** | 由 Facebook 开发，旨在解决 npm 早期版本中的性能问题。<br>发布于 2016 年，迅速流行，现已成为主流工具之一。 |

## 安装

| **工具** | **安装**                          | **验证**  |
| -------- | --------------------------------- | --------- |
| **npm**  | 安装 Node.js 时自带，无需单独安装 | `npm -v`  |
| **yarn** | `npm install -g yarn`             | `yarn -v` |

## 性能
| 特性         | npm                   | yarn                   |
| ------------ | --------------------- | ---------------------- |
| **安装速度** | 较慢                  | 更快                   |
| **缓存机制** | 支持缓存，但效率较低  | 高效缓存，减少重复下载 |
| **离线模式** | 支持，但体验不如 yarn | 支持，体验更好         |

**说明**：
- 安装速度：
  - `npm` 在依赖解析和安装顺序上的优化有限，通常是逐个安装依赖包，导致整体速度较慢。
  - `Yarn` 采用了并行安装的方式，可以同时下载多个依赖包，大大提高了安装速度。此外，Yarn 的依赖解析算法也更高效。

## 依赖管理
依赖管理是指如何安装、存储和解析项目所需的第三方库（也称为“包”或“依赖”）。
| 特性             | npm                                                              | yarn                                                                       |
| ---------------- | ---------------------------------------------------------------- | -------------------------------------------------------------------------- |
| **依赖锁定文件** | `package-lock.json`                                              | `yarn.lock`                                                                |
| **扁平化依赖**   | 支持，但可能导致依赖冲突（会因为不同包依赖的版本不同而产生冲突） | 支持，依赖解析更稳定（在解析依赖时会尽量选择兼容的版本，减少冲突的可能性） |
| **依赖版本管理** | 支持 `^`和 `~` 等版本范围                                        | 支持 `^` 和 `~` 等版本范围                                                 |

**说明**：
- 扁平化依赖：所有依赖包安装在同一层级，而不是嵌套安装。
- 依赖版本管理：
  - `^` 表示允许安装兼容的最新版本（例如 `^1.2.3` 允许安装 `1.x.x`，但不允许 `2.x.x`）
  - `~` 表示允许安装兼容的次版本（例如 `~1.2.3` 允许安装 `1.2.x`，但不允许 `1.3.x`）

## 特性
| 特性            | npm                        | yarn                             |
| --------------- | -------------------------- | -------------------------------- |
| **Workspaces**  | 支持                       | 支持                             |
| **Plug'n'Play** | 不支持                     | 支持（减少 `node_modules` 体积） |
| **脚本命令**    | `npm run <script>`         | `yarn <script>`                  |
| **全局包管理**  | `npm install -g <package>` | `yarn global add <package>`      |

**说明**：
- Workspaces：是一种管理 Monorepo（多包仓库）的方式，允许在一个项目中管理多个子包，并共享依赖。`npm` 从 `v7` 开始原生支持 Workspaces，但功能相对简单；`Yarn` 则更成熟，提供了更强大的依赖管理和优化，适合复杂的 Monorepo 项目。
- Plug'n'Play：
  - `npm` 依赖传统的 `node_modules` 文件夹来存储依赖包，这可能会导致文件夹体积庞大和性能问题。
  - `Plug'n'Play` 是 `Yarn` 的一项创新特性，就像是一个“智能引用系统”，通过直接引用依赖包的缓存文件，避免了创建庞大的 `node_modules` 文件夹，从而减少了磁盘空间占用和安装时间。
- 脚本命令 & 全局包管理：`npm` 和 `yarn` 的脚本命令和全局包管理功能基本相同，只是语法略有差异。

## 兼容性
| 特性            | npm                        | yarn                          |
| --------------- | -------------------------- | ----------------------------- |
| **与 npm 兼容** | 完全兼容                   | 完全兼容（使用 `npm` 的包源） |
| **跨平台支持**  | 支持 Windows、macOS、Linux | 支持 Windows、macOS、Linux    |

**说明**：两者完全兼容，但如果在同一个项目中交替使用 `npm` 和 `yarn`，可能会导致依赖锁定文件不一致，从而引发依赖版本冲突或安装错误。所以在团队协作中，统一使用一种包管理工具可以避免兼容性问题，确保所有开发者的环境一致。

## 社区与生态
| 特性         | npm                | yarn                     |
| ------------ | ------------------ | ------------------------ |
| **社区支持** | 生态庞大，文档完善 | 社区活跃，文档完善       |
| **插件生态** | 插件丰富           | 插件较少，但内置功能强大 |

**总结**：`npm` 的生态更庞大，但 `yarn` 的内置功能足够强大。

##  常用命令对比
| 功能                     | npm 命令                                | yarn 命令                       |
| ------------------------ | --------------------------------------- | ------------------------------- |
| 初始化项目               | `npm init`                              | `yarn init`                     |
| 查看依赖（已安装）       | `npm list`/`npm ls`                     | `yarn list`                     |
| 查看依赖（全局）         | `npm list -g --depth=0`                 | `yarn global list`              |
| 安装依赖（根据依赖文件） | `npm install`                           | `yarn install`                  |
| 安装依赖（特定版本）     | `npm install <package>@<version>`       | `yarn add <package>@<version>`  |
| 安装依赖（开发依赖）     | `npm install <package> --save-dev`      | `yarn add <package> --dev`      |
| 安装依赖（可选依赖）     | `npm install <package> --save-optional` | `yarn add <package> --optional` |
| 添加依赖                 | `npm install <package>`                 | `yarn add <package>`            |
| 删除依赖                 | `npm uninstall <package>`               | `yarn remove <package>`         |
| 检查依赖安全性           | `npm audit`                             | `yarn audit`                    |
| 修复依赖漏洞             | `npm audit fix`                         | `yarn audit --fix`              |
| 列出过时依赖             | `npm outdated`                          | `yarn outdated`                 |
| 更新依赖                 | `npm update`                            | `yarn upgrade`                  |
| 查看脚本                 | `npm run`                               | `yarn run`                      |
| 运行脚本                 | `npm run <script>`                      | `yarn <script>`                 |
| 全局安装                 | `npm install -g <package>`              | `yarn global add <package>`     |
| 清理缓存                 | `npm cache clean --force`               | `yarn cache clean`              |
| 查看包详情               | `npm view <package>`                    | `yarn info <package>`           |
| 查看包依赖树             | `npm ls <package>`                      | `yarn why <package>`            |
| 查看包文档               | `npm docs <package>`                    | `yarn docs <package>`           |

## 迁移建议
如果希望从在用的 `npm` 迁移到 `yarn` ，可以参考：
1. 清理旧文件
```bash
# 删除 node_modules 文件夹
rm -rf node_modules
# 删除 package-lock.json 文件
rm package-lock.json
```
2. 初始化 Yarn
```bash
# 运行 yarn install 命令，生成 yarn.lock 文件
yarn install
```
3. 更新脚本命令

将项目中的脚本命令从 `npm run <script>` 改为 `yarn <script>`。
