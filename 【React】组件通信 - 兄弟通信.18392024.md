# 组件通信 - 兄弟通信

## 思路
- 借助 “状态提升” 机制，通过父组件进行兄弟组件之间的数据传递。

## 步骤
- A组件先通过子传父的方式把数据传给父组件App
- 父组件App拿到数据后通过父传子的方式再传递给B组件

## 示例
- 
```js
import { useState } from "react"

function A({ onGetAName }) {
  const name = "this is A name"
  return (
    <div>
      this is A component,
      <button onClick={() => onGetAName(name)}>send</button>
    </div>
  )
}

function B({ name }) {
  return (
    <div>
      this is B component,
      {name}
    </div>
  )
}

function App() {

  const [name, setName] = useState('')

  const getAName = (name) => {
    console.log(name);
    setName(name)
  }

  return (
    <div>
      this is App
      <A onGetAName={getAName} />
      <B name={name} />
    </div>
  );
}
...
```