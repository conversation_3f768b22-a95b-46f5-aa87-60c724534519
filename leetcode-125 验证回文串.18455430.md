如果在将所有大写字符转换为小写字符、并移除所有非字母数字字符之后，短语正着读和反着读都一样。则可以认为该短语是一个 回文串 。
字母和数字都属于字母数字字符。
给你一个字符串 s，如果它是 回文串 ，返回 true ；否则，返回 false 。

示例 1：
输入: s = "A man, a plan, a canal: Panama"
输出：true
解释："amanaplanacanalpanama" 是回文串。

示例 2：
输入：s = "race a car"
输出：false
解释："raceacar" 不是回文串。

示例 3：
输入：s = " "
输出：true
解释：在移除非字母数字字符之后，s 是一个空字符串 "" 。
由于空字符串正着反着读都一样，所以是回文串。

提示：
1 <= s.length <= 2 * 105
s 仅由可打印的 ASCII 字符组成

实现：
```go
import "strings"

func isPalindrome(s string) bool {

    // 转换大写字母
    lower_str := strings.ToLower(s)

    // 定义左右指针
    left := 0
    right := len(lower_str) - 1

    // 移动指针并检查对应值，如果相等，继续移动指针，如果不等，则返回false
    for left < right {

        for left < right && !isalnum(lower_str[left]) {
            left++
        }

        for left < right && !isalnum(lower_str[right]) {
            right--
        }

        if lower_str[left] != lower_str[right] { 
            return false
        }

        left++
        right--
    }

    return true

}

func isalnum(ch byte) bool {
    return (ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9')
}
```