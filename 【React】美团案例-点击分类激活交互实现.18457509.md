# 点击分类激活交易实现

## 步骤分析
- 使用RTK编写管理activeIndex
- 组件中点击触发action更改activeIndex
- 动态控制激活类名显示

## 实现示例

### 使用RTK编写管理activeIndex：takeaway.js
```js
// 编写store
import { createSlice } from '@reduxjs/toolkit'
import axios from 'axios'

const foodsStore = createSlice({
    name: 'foods',
    initialState: {
        // 商品列表
        foodsList: [],
        // 菜单激活下标值
        activeIndex: 0
    },
    reducers: {
        // 更改商品列表
        setFoodsList(state, action) {
            state.foodsList = action.payload
        },
        // 更改activeIndex
        changeActisveIndex(state, action) {
            state.activeIndex = action.payload
        }
    }
})

// 异步获取部分（解构修改方法）
const { setFoodsList, changeActisveIndex } = foodsStore.actions // 解构foodsStore方法

const fetchFoodsList = () => {
    return async (dispatch) => {
        // 编写异步逻辑
        const res = await axios.get('http://localhost:3004/takeaway')
        // 调用dispatch函数提交action
        dispatch(setFoodsList(res.data))
    }
}

export { fetchFoodsList, changeActisveIndex }

const reducer = foodsStore.reducer

export default reducer
```

### menu/index.js
```js
import classNames from 'classnames'
import { useDispatch, useSelector } from 'react-redux'
import { changeActisveIndex } from '../../store/modules/takeaway';
import './index.scss'

const Menu = () => {
  const { foodsList, activeIndex } = useSelector(state => state.foods)
  const dispatch = useDispatch()
  const menus = foodsList.map(item => ({ tag: item.tag, name: item.name }))
  return (
    <nav className="list-menu">
      {/* 添加active类名会变成激活状态 */}
      {menus.map((item, index) => {
        return (
          <div
            onClick={() => dispatch(changeActisveIndex(index))}
            key={item.tag}
            className={classNames(
              'list-menu-item',
              activeIndex === index && 'active'
            )}
          >
            {item.name}
          </div>
        )
      })}
    </nav>
  )
}

export default Menu
```