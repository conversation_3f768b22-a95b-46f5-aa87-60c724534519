# classnames

## 介绍
- 是一个简单的JS库，可以非常方便地通过条件控制类名的显示

## 使用

### 官网
- [https://github.com/JedWatson/classnames](https://github.com/JedWatson/classnames)

### 安装
- `npm install classnames`

### 示例
####  字符串拼接（不推荐）
- 
    ```js
    ...
    className={`nav-item ${type === item.type && 'active'}`}
    ...
    ```
#### 使用 classnames
- 
    ```js
    import ClassNames from 'classnames'
    ...
    className={ClassNames('nav-item', {active: type === item.type})}
    ...
    ```
