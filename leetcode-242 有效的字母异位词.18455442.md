给定两个字符串 s 和 t ，编写一个函数来判断 t 是否是 s 的 字母异位词。

示例 1:
输入: s = "anagram", t = "nagaram"
输出: true

示例 2:
输入: s = "rat", t = "car"
输出: false

提示:
1 <= s.length, t.length <= 5 * 104
s 和 t 仅包含小写字母

实现：
```go
func isAnagram(s string, t string) bool {

    // 如果s和t的长度不一致，或者长度为0，则返回false
    if len(s) != len(t) || len(s) == 0 {
        return false
    }

    // 定义字符计数的映射
    char_count := make(map[rune]int)

    // 统计s中不同字符的个数
    for _, schar := range s {
        char_count[schar]++
    }

    // 针对已经统计的字符个数，
    for _, tchar := range t {
        char_count[tchar]--
        if char_count[tchar] < 0 {
            return false
        }
    }

    return true
}
```