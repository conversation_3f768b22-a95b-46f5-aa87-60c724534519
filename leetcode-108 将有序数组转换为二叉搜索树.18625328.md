给你一个整数数组 nums ，其中元素已经按 升序 排列，请你将其转换为一棵平衡二叉搜索树。

示例 1：
![image](https://img2024.cnblogs.com/blog/1548975/202412/1548975-20241223235813540-1312339374.png)

> 输入：nums = [-10,-3,0,5,9]
输出：[0,-3,9,-10,null,5]
解释：[0,-10,5,null,-3,null,9] 也将被视为正确答案：
![image](https://img2024.cnblogs.com/blog/1548975/202412/1548975-20241223235907650-1639450484.png)

示例 2：
![image](https://img2024.cnblogs.com/blog/1548975/202412/1548975-20241223235915900-1593986145.png)

> 输入：nums = [1,3]
输出：[3,1]
解释：[1,null,3] 和 [3,1] 都是高度平衡二叉搜索树。

提示：
- 1 <= nums.length <= 104
- -104 <= nums[i] <= 104
- nums 按 严格递增 顺序排列

go实现：
```go
// 定义二叉树节点结构
type TreeNode struct {
	Val   int
	Left  *TreeNode
	Right *TreeNode
}

// 主函数，用于将有序数组转为高度平衡二叉树
func sortedArrayToBST(nums []int) *TreeNode {
	// 直接返回辅助函数结果
	return buildBST(nums, 0, len(nums)-1)
}

// 辅助函数，用于构建二叉树，参数为给定数组nums，及构建范围左右边界
func buildBST(nums []int, left, right int) *TreeNode {

	// 当左边界大于右边界时，表示搜索区域内无可构建元素，于是返回空对象
	if left > right {
		return nil
	}

	// 获取数组中位数作为根节点，因为数组为升序排序，故其中位数符合二叉树节点值要求，即左边区域均小于节点，右边区域均大于节点
	mid := left + (right-left)/2

	// 创建根节点root，其中节点值为给定数组范围中位数，这里是变量地址拷贝
	root := &TreeNode{Val: nums[mid]}

	// 通过调用函数自身的方式递归创建左节点树（中间值左边部分）
	root.Left = buildBST(nums, left, mid-1)

	// 创建右节点树（中间值右边部分）
	root.Right = buildBST(nums, mid+1, right)

	// 返回创建结果
	return root
}
```