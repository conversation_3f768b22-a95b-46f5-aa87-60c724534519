给你一个整数数组 nums，返回 数组 answer ，其中 answer[i] 等于 nums 中除 nums[i] 之外其余各元素的乘积 。

题目数据 保证 数组 nums之中任意元素的全部前缀元素和后缀的乘积都在  32 位 整数范围内。

请 不要使用除法，且在 O(n) 时间复杂度内完成此题。

示例 1:
> 输入: nums = [1,2,3,4]
输出: [24,12,8,6]

示例 2:
> 输入: nums = [-1,1,0,-3,3]
输出: [0,0,9,0,0]

提示：
- 2 <= nums.length <= 105
- -30 <= nums[i] <= 30
- 保证 数组 nums之中任意元素的全部前缀元素和后缀的乘积都在  32 位 整数范围内

Go实现：
```go
func productExceptSelf(nums []int) []int {

	// 获取nums长度
	n := len(nums)

	// 定义返回结果answer，数据类型为切片
	answer := make([]int, n)

	// 定义前缀乘积变量prefix，和后缀乘积变量suffix，且初始值均为1
	prefix, suffix := 1, 1

	// 第一次从前往后遍历，用来计算前缀乘积和
	for i := 0; i < n; i++ {
		// 如果当前元素不是首元素，则prefix为之前乘积与前一个元素的乘积和
		if i != 0 {
			prefix *= nums[i-1]
		}
		// 第一轮answer各元素仍为初始值，故将计算后prefix直接赋值给answer当前元素即可
		answer[i] = prefix
	}

	// 第二次从后往前遍历，在已计算前缀乘积基础上，继续乘以后缀乘积得到最终结果
	for j := n - 1; j >= 0; j-- {
		// 如果当前元素不是尾元素，则suffix为之前乘积与后一个元素的乘积和
		if j != n-1 {
			suffix *= nums[j+1]
		}
		// 第二轮answer各元素初始值为前缀乘积，故与计算结果suffix相乘后再重新赋值给answer当前元素，即表示除nums当前元素外的前缀和后缀乘积和
		answer[j] = answer[j] * suffix
	}

	// 返回最后计算结果
	return answer
}
```