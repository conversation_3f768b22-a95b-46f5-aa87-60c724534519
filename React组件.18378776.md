# react组件

## 概念
- 一个组件就是用户界面的一部分，它可以有自己的逻辑和外观，组件之间可以相互嵌套，也可以复用多次。

## 定义组件
- 在 React 中，一个组件就是首字母大写的函数，内部存放了组件的逻辑和视图UI，渲染组件只需要把组件当成标签书写即可。
- ```js
    function Button () {
        return <button>Click me!</button>;
    }
    ```

## 使用组件
- 自闭和标签
    - ```js
        function App() {
            return (
                <div className="App">
                    <Button />
                </div>
            );
        }
        ```
- 成对标签
    - ```js
        function App() {
            return (
                <div className="App">
                    <Button></Button>
                </div>
            );
        }
        ```