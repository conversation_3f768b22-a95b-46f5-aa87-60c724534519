给定一种规律 pattern 和一个字符串 s ，判断 s 是否遵循相同的规律。
这里的 遵循 指完全匹配，例如， pattern 里的每个字母和字符串 s 中的每个非空单词之间存在着双向连接的对应规律。

示例1:
输入: pattern = "abba", s = "dog cat cat dog"
输出: true

示例 2:
输入:pattern = "abba", s = "dog cat cat fish"
输出: false

示例 3:
输入: pattern = "aaaa", s = "dog cat cat dog"
输出: false

提示:
1 <= pattern.length <= 300
pattern 只包含小写英文字母
1 <= s.length <= 3000
s 只包含小写英文字母和 ' '
s 不包含 任何前导或尾随对空格
s 中每个单词都被 单个空格 分隔

实现：
```go
import "strings"

func wordPattern(pattern string, s string) bool {

    // 定义映射容器
    char_to_word := make(map[rune]string)
    word_to_char := make(map[string]rune)

    // 按空格分隔单词
    words := strings.Fields(s)

    // 如果pattern长度和单词列表不一致，则返回错误
    if len(pattern) != len(words) {
        return false
    }

    // 开始遍历
    for i, char := range pattern {

        // 如果映射字符存在，则比较映射字符的值是否和单词列表是否一致，不存在则新增映射
        if mapper_word, ok := char_to_word[char]; ok {
            if mapper_word != words[i] {
                return false
            }
        } else {
            char_to_word[char] = words[i]
        }

        // 如果映射单词存在，则比较映射单词的值是否和当前遍历字符是否一致，不存在则新增映射
        word := words[i]
        if mapper_char, ok := word_to_char[word]; ok {
            if mapper_char != char {
                return false
            }
        } else {
            word_to_char[word] = char
        }
    }

    // 遍历完毕无异常，则返回true
    return true
}
```