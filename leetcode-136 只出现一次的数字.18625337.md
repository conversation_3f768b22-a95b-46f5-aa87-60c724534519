给你一个 非空 整数数组 nums ，除了某个元素只出现一次以外，其余每个元素均出现两次。找出那个只出现了一次的元素。

你必须设计并实现线性时间复杂度的算法来解决此问题，且该算法只使用常量额外空间。

示例 1 ：
> 输入：nums = [2,2,1]
输出：1

示例 2 ：
> 输入：nums = [4,1,2,1,2]
输出：4

示例 3 ：
> 输入：nums = [1]
输出：1

提示：
- 1 <= nums.length <= 3 * 104
- -3 * 104 <= nums[i] <= 3 * 104
- 除了某个元素只出现一次以外，其余每个元素均出现两次。

go实现：
```go
func singleNumber(nums []int) int {

	// 定义返回结果，初始值为0
	result := 0

	// 对给定数组进行遍历，并利用异或计算中数字与自身异或为0的规则，对每个当前元素进行异或计算
	for _, num := range nums {
		result ^= num
	}

	// 结束遍历，返回计算后的结果
	return result
}
```