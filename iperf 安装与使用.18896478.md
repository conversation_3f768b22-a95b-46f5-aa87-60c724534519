# 简介

## 定义
iperf是一款开源的网络性能测量工具，支持 TCP/UDP/SCTP 协议，用于测量带宽、延迟、丢包等网络指标。

## 主要作用
1. 验证网络设备实际性能（路由器/交换机）  
2. 排查带宽不足问题  
3. 云计算环境网络质量评估  
4. 网络优化前后对比测试

# 安装

## 离线安装
官网下载地址：[https://iperf.fr/iperf-download.php](https://iperf.fr/iperf-download.php)

## 在线安装

**Debian/Ubuntu**
```bash
sudo apt install iperf3 -y
```

**CentOS/RHEL**
```bash
sudo yum install iperf3 -y
```

**macOS**
```bash
brew install iperf3
```

# 基础用法

工具版本：`3.5`

## 常用参数

**通用参数（Server/Client 共用）**

| 参数             | 说明                                     | 默认值                              | 用法示例             | 适用场景                                     |
| ---------------- | ---------------------------------------- | ----------------------------------- | -------------------- | -------------------------------------------- |
| `-p, --port`     | 指定服务端监听/客户端连接的端口          | `5201`                              | `-p 8080`            | 非标准端口测试（需确保防火墙放行）           |
| `-f, --format`   | 强制指定输出单位 （k=Kbit, M=Mbit等）    | 自适应                              | `-f M`               | 明确以指定单位显示结果                       |
| `-i, --interval` | 设置周期性报告的间隔（秒）               | `1`                                 | `-i 2`               | 实时监控带宽波动                             |
| `-l, --length`   | 设置 TCP 发送缓冲区长度或 UDP 数据包大小 | TCP：128K<br>UDP：1470(v4)/1450(v6) | `-l 1K`              | 模拟特定业务流量（小包/大包）、测试MTU兼容性 |
| `-B, --bind`     | 绑定到指定网络接口的 IP 地址             | /                                   | `-B ***********00`   | 多网卡环境下的指定接口测试                   |
| `-J, --json`     | 输出 JSON 格式结果，默认输出为文本格式   | /                                   | `-J`                 | 自动化脚本解析测试结果                       |
| `--logfile`      | 将输出保存到日志文件，默认输出到终端     | /                                   | `--logfile test.log` | 长期测试或无人值守场景                       |
| `-V, --verbose`  | 显示详细调试信息                         | 关闭                                | `-V`                 | 排查连接或配置问题                           |

**服务端专用参数**

| 参数                     | 说明                             | 默认值 | 用法示例                         | 适用场景                               |
| ------------------------ | -------------------------------- | ------ | -------------------------------- | -------------------------------------- |
| `-s, --server`           | 启动服务端模式，必须显式指定     | /      | `iperf3 -s`                      | 基础服务端模式                         |
| `-D, --daemon`           | 后台守护进程模式，默认为前台运行 | 关闭   | `iperf3 -s -D`                   | 长期运行服务端                         |
| `-1, --one-off`          | 处理单个连接后退出               | 关闭   | `iperf3 -s -1`                   | 单次测试后自动关闭服务端               |
| `--rsa-private-key-path` | 指定 RSA 私钥路径（加密认证）    | /      | `--rsa-private-key-path key.pem` | 加密测试环境，需配套客户端参数才能生效 |

**客户端专用参数**

| 参数               | 说明                                       | 默认值                           | 用法示例                | 适用场景                                      |
| ------------------ | ------------------------------------------ | -------------------------------- | ----------------------- | --------------------------------------------- |
| `-c, --client`     | 指定服务端地址并启动客户端（必须显式指定） | /                                | `iperf3 -c ***********` | 基本客户端模式                                |
| `-u, --udp`        | 使用 UDP 协议测试                          | TCP                              | `-u -b 100M`            | 实时性/丢包测试                               |
| `-b, --bitrate`    | 设置目标带宽                               | UDP：`1M`<br>TCP：无限制         | `-b 200M`               | 模拟高负载或限速测试                          |
| `-t, --time`       | 自定义测试持续时间（秒）                   | `10`                             | `-t 30`                 | 延长测试时间                                  |
| `-P, --parallel`   | 多线程并发                                 | `1`                              | `-P 4`                  | 多线程测试网络吞吐量上限                      |
| `-R, --reverse`    | 反向模式（服务端发数据，客户端收）         | 关闭                             | `-R`                    | 测试上行/下行带宽不对称场景                   |
| `-O, --omit`       | 忽略前 N 秒的测试结果                      | `0`                              | `-O 5`                  | 排除 TCP 慢启动阶段的波动数据                 |
| `-C, --congestion` | 指定 TCP 拥塞控制算法                      | 系统默认（如 `cubic`）           | `-C bbr`                | 对比不同算法性能（如 BBR、CUBIC）             |
| `-Z, --zerocopy`   | 使用零拷贝技术发送数据                     | 关闭                             | `-Z`                    | 高性能服务器优化测试（需系统支持&管理员权限） |
| `-w, --window`     | 设置套接字缓冲区（socket buffer）大小      | 系统默认（基于系统内核动态调整） | `-w 2M`                 | 优化高延迟或高带宽网络性能                    |

⚠️ **注意事项**：
- `-u` 会触发 UDP 的默认限速（1Mbps），而 TCP 不限速。
-  部分算法（如 `bbr` ）会动态调整窗口，可能覆盖 `-w` 的手动设置，需要测试时固定算法（如 `-C cubic`）。

## 典型场景

**环境信息**：
- 客户端：`***********00`
- 服务端：`*************`

#### 基础带宽测试

**目标**
评估网络链路的基础性能。

**使用命令**
```bash
# 服务端
iperf3 -s

# 客户端
iperf3 -c ************* -t 60
```
参数说明：
- `-t 60`：延长测试时间至 60 秒。

**输出示例**
```plaintext
[ ID] Interval           Transfer     Bitrate         Retr
[  5]   0.00-60.00  sec  6.61 GBytes   946 Mbits/sec  241             sender
[  5]   0.00-60.00  sec  6.61 GBytes   946 Mbits/sec                  receiver
```
- `Transfer`：总传输数据量，如 `6.61 GBytes` 。
- `Bitrate`：平均带宽，如 `946 Mbits/sec` 。

**主要关注点**
- 测试前关闭防火墙或确保端口放行。
- 多次运行取平均值，避免偶然性。
- 评估实际带宽是否接近理论最大值，如 946Mb/s 表示接近千兆网卡（TCP/IP协议开销会导致实际带宽略低于理论最大值）。 

#### 不同TCP拥塞控制算法对比

**目标**
评估不同拥塞控制算法对带宽的影响（确保服务端和客户端的操作系统都支持指定的拥塞控制算法）。

**使用命令**
```bash
# 服务端
iperf3 -s

# 客户端（分别测试两种算法）
iperf3 -c ************* -C cubic -t 30
iperf3 -c ************* -C bbr -t 30
```

**主要关注点**
- 两种算法的平均带宽和重传次数（Retr）。 
- 确保服务端和客户端的操作系统都支持指定的拥塞控制算法，在Linux中，可使用 `sysctl net.ipv4.tcp_available_congestion_control` 检查支持的算法。

#### TCP窗口大小高延迟对比

**目标**
优化高延迟下的带宽利用率。

**使用命令**
```bash
# 服务端
iperf3 -s

# 客户端
iperf3 -c ************* -w 2M
```
参数说明：
- `-w 2M`：窗口大小为2M。

**主要关注点**
- 窗口大小是否足够（带宽=窗口大小/RTT）。 

#### UDP流量测试

**目标**
评估网络对UDP流量的承载能力。

**使用命令**
```bash
# 服务端
iperf3 -s

# 客户端
iperf3 -c ************* -u -b 50M
```
参数说明：
- `-u`：使用 udp 协议，模拟无连接、低延迟的实时数据传输。
- `-b 50M`：指定带宽为 50M（接近 4K HDR 视频流的 20-50 Mbps 典型码率），注意实际码率因编码而异。

**输出示例**
```plaintext
[ ID] Interval           Transfer     Bitrate         Jitter    Lost/Total Datagrams
[  5]   0.00-10.00  sec  59.6 MBytes  50.0 Mbits/sec  0.000 ms  0/42806 (0%)  sender
[  5]   0.00-10.00  sec  59.6 MBytes  50.0 Mbits/sec  0.098 ms  0/42806 (0%)  receiver
```
- `Jitter`：数据包到达时间的波动，流畅播放应低于 30ms。
- `Lost/Total`：丢包率，超过1%可能导致视频卡顿。

**主要关注点**

#### 上下行测试

**目标**
验证网络上下行双向传输时的网络带宽总和（可分开独立的客户端进程同时测试）。

**使用命令**
```bash
# 服务端
iperf3 -s

# 客户端
iperf3 -c ************* -t 30  # 上行
iperf3 -c ************* -t 30 -R # 下行
```
参数说明：
- `-R`：反向模式，客户端反过来接收数据，服务端发送数据到客户端，即下行带宽。

**输出示例**
```plaintext
[ ID] Interval           Transfer     Bitrate
[  5]   0.00-10.11  sec  70.5 MBytes  58.5 Mbits/sec                  sender
[  5]   0.00-10.11  sec  68.0 MBytes  56.4 Mbits/sec                  receiver
[  7]   0.00-10.11  sec  68.2 MBytes  56.6 Mbits/sec                  sender
[  7]   0.00-10.11  sec  68.0 MBytes  56.4 Mbits/sec                  receiver
[SUM]   0.00-10.11  sec   139 MBytes   115 Mbits/sec                  sender
[SUM]   0.00-10.11  sec   136 MBytes   113 Mbits/sec                  receiver
```

**主要关注点**
- **上行和下行带宽**：观察上行和下行两个方向的 Bitrate，验证网络对称性。
- **总带宽占用**：若双向带宽总和接近链路上限，可能引发拥塞。

#### 多线程压测（模拟多用户）

**目标**
模拟多用户并发访问，测试网络在高负载下的吞吐量和稳定性

**使用命令**
```bash
# 服务端
iperf3 -s

# 客户端
iperf3 -c ************* -P 4
```
参数说明：
- `-P 4`：动 4 个并行连接，模拟多用户同时传输数据。

**输出示例**
```plaintext
[ ID] Interval           Transfer     Bitrate         Retr
[  5]   0.00-10.00  sec   249 MBytes   209 Mbits/sec  101             sender
[  5]   0.00-10.00  sec   248 MBytes   208 Mbits/sec                  receiver
[  7]   0.00-10.00  sec   245 MBytes   206 Mbits/sec  102             sender
[  7]   0.00-10.00  sec   244 MBytes   205 Mbits/sec                  receiver
[  9]   0.00-10.00  sec   256 MBytes   214 Mbits/sec   96             sender
[  9]   0.00-10.00  sec   254 MBytes   213 Mbits/sec                  receiver
[ 11]   0.00-10.00  sec   383 MBytes   321 Mbits/sec  149             sender
[ 11]   0.00-10.00  sec   381 MBytes   320 Mbits/sec                  receiver
[SUM]   0.00-10.00  sec  1.11 GBytes   950 Mbits/sec  448             sender
[SUM]   0.00-10.00  sec  1.10 GBytes   946 Mbits/sec                  receiver
```

**主要关注点**
- **单个连接带宽分布**：若某个连接显著低于其他，可能存在负载不均衡或单线程瓶颈。