给你一个二叉搜索树的根节点 root ，返回 树中任意两不同节点值之间的最小差值 。

差值是一个正数，其数值等于两值之差的绝对值。

示例 1：
![image](https://img2024.cnblogs.com/blog/1548975/202412/1548975-20241223235612612-1766151555.png)

> 输入：root = [4,2,6,1,3]
输出：1

示例 2：
![image](https://img2024.cnblogs.com/blog/1548975/202412/1548975-20241223235625849-1226277147.png)

> 输入：root = [1,0,48,null,null,12,49]
输出：1

提示：
- 树中节点的数目范围是 [2, 104]
- 0 <= Node.val <= 105

go实现：
```go
/**
 * Definition for a binary tree node.
 * type TreeNode struct {
 *     Val int
 *     Left *TreeNode
 *     Right *TreeNode
 * }
 */
// 利用二叉搜索树中序遍历有序的特性，比较相邻节点差值
func getMinimumDifference(root *TreeNode) int {

	// 初始化最小差值为最大整数
	minDiff := math.MaxInt32

	// 使用指针保存前驱节点的值，确保在递归调用间保持状态，初始为nil表示尚未设置，这样处理第一个节点时不会计算差值
	var prev *int

	// 定义闭包实现中序遍历
	var inorder func(node *TreeNode)

	inorder = func(node *TreeNode) {
		if node == nil {
			return
		}

		// 递归处理左子树（BST的较小值部分）
		inorder(node.Left)

		// 核心比较逻辑
		if prev != nil {
			// 计算当前节点值与前驱节点值的绝对差，由于中序遍历保证升序，直接相减即可得到正差值
			currentDiff := node.Val - *prev
			// 更新最小差值（由于中序有序，不需要取绝对值）
			if currentDiff < minDiff {
				minDiff = currentDiff
			}

		}

		// 更新前驱节点指针为当前节点值（关键：跨递归保持状态）
		prev = &node.Val

		// 递归处理右子树（BST的较大值部分）
		inorder(node.Right)

	}

	// 启动中序遍历
	inorder(root)

	return minDiff

}
```