# 模型偏差
- model bias：由于models自身的限制，导致无法模拟真实的状况，比如 linear models have severe limitation（线性模型就有严重的局限性），所以需要一个更复杂，但更有弹性的模型。
- sigmoid function
    - 说明：S型函数，通过调整参数b、w和c，从而逼近其他不同的函数。
    - 基本公式：$y = c \frac{1}{1+e^-(b+wx_1)} = c sigmoid(b+wx_1)$
        - 参数w：change slopes 改变斜率
        - 参数b：shift 改变位移
        - 参数c：change height 改变高度

# 减少模型偏差
- 方式：利用线性代数简化神经网络的计算表示，即简化成矩阵与向量的相乘。
- 数学推导：
    - 推导1：$y = b + \sum_i c_j sigmoid\left(b_i + \sum_j w_{ij}x_j\right) $
    - 推导2：$r_{1,2,3} = b_{1,2,3} + w_{12,22,32}x_2 + w_{13,23,33}x_3$
    - 推导3：$\begin{bmatrix}r_1\\r_2\\r_3\end{bmatrix} = \begin{bmatrix}b_1\\b_2\\b_3\end{bmatrix} + \begin{bmatrix}w_{11}&&w_{12}&&w_{13}\\w_{21}&&w_{22}&&w_{23}\\w_{31}&&w_{32}&&w_{33}\end{bmatrix}\begin{bmatrix}x_1\\x_2\\x_3\end{bmatrix}$
    - 推导4：$r = b + W x$
- 线性代数表示：$y = b + c^T \sigma(b+wx)$

# 未知参数求解与优化
- 背景：在参数数量庞大时，无法通过穷举所有可能的参数值来寻找最小损失，而需要使用梯度下降等优化技巧来找到最优参数。

# Sigmoid函数
- 在机器学习模型中，Sigmoid函数的数量可以由使用者自行决定，数量的多少影响模型能够拟合的复杂度。更多数量的Sigmoid函数可以生成更复杂的分段线性函数，从而逼近更复杂的函数。

# 损失函数
- 定义：在机器学习中，损失（loss）指的是模型预测结果与实际标签之间的差异度量，用来评估模型的表现。损失函数是衡量预测值与真实值之间差异的函数，通过最小化损失函数，模型可以学习到最佳的参数，从而提高预测的准确性。
- 优化方式：通过计算梯度并更新参数来最小化损失函数。
- 最优损失数学表达：$\theta = \begin{bmatrix}\theta_1\\\theta_2\\\theta_3\\...\\\end{bmatrix} \rightarrow \theta^* = arg{min\choose\theta}L$
- 优化步骤：
    - 步骤1 - 随机选取初始值 $\theta^0$
    - 步骤2 - 第一次计算梯度 $g = \begin{bmatrix}\frac{\delta L}{\delta \theta_1} \mid \theta = \theta^0\\\frac{\delta L}{\delta \theta_2} \mid \theta = \theta^0\\...\\\end{bmatrix} \rightarrow g = \nabla L(\theta^0)$ （$ \theta^1 \leftarrow \theta^0 - \eta g$）
    - 步骤3 - 第二次计算梯度 $g = \nabla L(\theta^1)$（$ \theta^2 \leftarrow \theta^1 - \eta g$）
    - ...
    - 直到用户选择停止

# 更新和周期
- update（更新）：Update 指的是在机器学习中更新模型参数的过程。它基于损失函数的梯度来调整参数，以优化模型性能。
- update 过程：
  1. 计算损失函数的梯度，即模型预测值与真实值之间的差异。
  2. 使用梯度下降或其他优化算法，根据梯度调整参数。
  3. 重复上述步骤，直到模型收敛或达到预设的迭代次数。
- epoch（周期）：Epoch 指的是一次完整的训练过程，即使用整个训练数据集进行一次完整的迭代。
- epoch 过程：
  1. 对于每个批次的数据，计算损失函数的梯度。
  2. 使用梯度下降算法更新模型中所有参数的值。
  3. 重复上述步骤，直到所有批次的数据都处理完毕。
- 区别：
  - Update 和 Epoch 是机器学习中训练模型的关键步骤。
  - Update 是 Epoch 的组成部分。
  - 通过多次迭代 Update 和 Epoch，可以训练出性能更好的模型。

# 超参数
- 定义：Hyperparameter（超参数）是机器学习模型中需要手动设置的参数，它们不直接从数据中学习，而是由用户根据经验或先验知识进行设定。
- 超参数类型：
  - 学习率：控制模型参数更新的步长，影响模型的收敛速度和稳定性。
  - 正则化参数：用于防止模型过拟合，例如 L1 正则化、L2 正则化等。
  - 批量大小（batch size）：控制每次迭代的训练样本数量。
  - 迭代次数：控制训练过程的长度。

# 激活函数
- 定义：Activation function（激活函数）是机器学习模型中用于将线性组合的输出转换为所需输出范围的函数。它通常用于神经网络中，将神经元的线性组合转换为非负输出，模拟神经元激活的过程。
- 作用：
  - 将线性组合转换为非线性输出：激活函数可以将线性组合的输出转换为非线性输出，使模型能够学习更复杂的函数关系。
  - 引入非线性因素：激活函数引入非线性因素，使模型能够更好地适应复杂数据。
  - 控制模型输出范围：激活函数可以控制模型输出的范围，使其符合实际需求。
- 常用类型：
  - 线性激活函数：如 线性函数（linear），恒等函数（identity）。
  - 非线性激活函数：如 sigmoid函数、ReLU函数。

# ReLU函数
- 定义：eLU（Rectified Linear Unit）函数，也称为线性整流函数，它将输入值 x 映射到 0 或 x，具体取决于 x 的正负，是一种简单高效的激活函数，在深度神经网络中应用广泛。它具有避免梯度消失、提高模型性能等优点，但也存在梯度消失和不连续性等缺点。
- 函数表达式：$y = max(0,x)$
  - 当 x > 0 时，y = x
  - 当 x ≤ 0 时，y = 0
-  实验数据独照：增加ReLU数量和调整模型结构（如增加运算次数）可以有效降低loss，提高预测的准确性。特别是在增加运算次数后，模型在训练资料和未见过的资料上的loss均有显著下降，且在特定日期的预测中，模型能较为准确地捕捉到数据的波动趋势。

# 神经网络模型命名影响
- Neural Network（神经网络）：神经网络是一种模仿人脑神经元结构和功能的计算模型，由多个相互连接的神经元组成。每个神经元接收输入，通过激活函数处理后输出结果，最终形成模型输出。
- Deep Learning（深度学习）：深度学习是一种利用深度神经网络进行学习的机器学习技术。它通过构建具有多层非线性变换的神经网络，从大量数据中自动学习复杂的特征和模式。
- 关系：
  - 深度学习是机器学习的一个分支：深度学习是神经网络的一种特定应用，它利用深度神经网络处理复杂数据和学习复杂模式。
  - 深度学习需要深度神经网络：深度学习需要构建具有多层非线性变换的神经网络，才能有效学习复杂特征和模式。
  - 深度学习具有更强大的能力：与传统的神经网络相比，深度学习模型具有更强的学习能力，能够处理更复杂数据和学习更复杂的模式。

# 过拟合
- 定义：Overfitting（过拟合）是指机器学习模型在训练数据上表现良好，但在未见过的数据上表现较差的现象。这通常发生在模型过于复杂，能够学习到训练数据中的噪声和细节，而无法泛化到新的数据，导致模型在实际应用中的表现不佳。
- 常见原因：
  - 模型复杂度过高：当模型过于复杂时，它能够学习到训练数据中的噪声和细节，导致模型在训练数据上表现良好，但在未见过的数据上表现较差。
  - 训练数据不足：当训练数据量不足时，模型可能无法学习到数据的真实分布，而是学习到训练数据中的噪声和细节。
  - 特征选择不当：不相关的特征或冗余特征可能导致模型学习到噪声，从而引起过拟合。
  - 正则化不足：正则化是一种防止过拟合的技术，当正则化不足时，模型容易过拟合。
- 优化方法：
  - 降低模型复杂度：使用更简单的模型，例如减少神经网络的层数或节点数。
  - 增加训练数据：收集更多训练数据，以帮助模型学习数据的真实分布。
  - 特征选择：选择相关的特征，去除不相关的特征或冗余特征。
  - 正则化：使用正则化技术，例如 L1 正则化、L2 正则化等，来惩罚模型的复杂度。
  - 交叉验证：使用交叉验证来评估模型的泛化能力。
  - 早停法：在训练过程中，当测试误差不再显著下降时，停止训练，以避免过拟合。

# Backpropagation
- 定义：反向传播，是训练神经网络的核心算法，用于高效计算损失函数对模型参数的梯度，从而通过优化算法（如梯度下降）调整参数，最小化预测误差。
- 核心思想：反向传播基于链式法则（Chain Rule），通过从输出层向输入层逐层反向传递误差信号，计算每一层权重和偏置的梯度。这种反向计算避免了直接计算所有参数的重复性，显著提升了效率。
- 计算步骤
  1. 前向传播（Forward Pass）：输入数据经过网络逐层计算，得到预测输出。
  2. 计算损失（Loss Calculation）：使用损失函数（如均方误差、交叉熵）衡量预测值与真实值的差距。
  3. 反向传播误差（Backward Pass）
    - 输出层梯度：计算损失对输出层输入的导数；
    - 逐层反向计算：利用链式法则，计算每一层的权重梯度和偏置梯度。
    - 使用梯度下降等优化算法更新参数。