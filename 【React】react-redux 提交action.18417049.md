# 提交action

## 需求说明
- 组件中有两个按钮 ’+10‘ 和 ‘+20’，可以直接把count值修改到对应的数字，目标count值是在组件中传递过去的，需要在提交action的时候提交参数。

## 需求实现
- 在 reducers 的同步修改方法中添加action对象参数，在调用actionCreater的时候传递参数，参数会被传递到action对象playload属性上。

### countrerStore.js
- 
```js
import { createSlice } from "@reduxjs/toolkit"

const counterStore = createSlice({
    name: 'counter',
    // 初始化state
    initialState: {
        count: 0
    },
    // 修改静态的方法 同步方法 支持直接修改
    reducers: {
        increment(state) {
            state.count++
        },
        decrement(state) {
            state.count--
        },
        AddToNum(state, action) {
            state.count += action.payload
        }
    }
})

// 解构出来 actionCreater 函数
const { increment, decrement, AddToNum } = counterStore.actions
// 获取reducer
const reducer = counterStore.reducer

// 以按需导出的方式导出 actionCreater
export { increment, decrement, AddToNum }
// 以默认导出的方式导出 reducer
export default reducer
```

### App.js
- 
```js
import { useSelector, useDispatch } from 'react-redux'

// 导入actionCreater
import { increment, decrement, AddToNum } from "./store/modules/counterStore";

function App() {
  const { count } = useSelector(state => state.counter)
  const dispatch = useDispatch()

  return (
    <div className="App">
      <button onClick={() => dispatch(decrement())}>-</button>
      {count}
      <button onClick={() => dispatch(increment())}>+</button>
      <button onClick={() => dispatch(AddToNum(10))}>+10</button>
      <button onClick={() => dispatch(AddToNum(20))}>+20</button>
    </div>
  );
}

export default App;

```