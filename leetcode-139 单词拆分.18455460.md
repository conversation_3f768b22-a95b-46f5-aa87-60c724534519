给你一个字符串 s 和一个字符串列表 wordDict 作为字典。如果可以利用字典中出现的一个或多个单词拼接出 s 则返回 true。
注意：不要求字典中出现的单词全部都使用，并且字典中的单词可以重复使用。

示例 1：
输入: s = "leetcode", wordDict = ["leet", "code"]
输出: true
解释: 返回 true 因为 "leetcode" 可以由 "leet" 和 "code" 拼接成。

示例 2：
输入: s = "applepenapple", wordDict = ["apple", "pen"]
输出: true
解释: 返回 true 因为 "applepenapple" 可以由 "apple" "pen" "apple" 拼接成。
     注意，你可以重复使用字典中的单词。

示例 3：
输入: s = "catsandog", wordDict = ["cats", "dog", "sand", "and", "cat"]
输出: false

提示：
1 <= s.length <= 300
1 <= wordDict.length <= 1000
1 <= wordDict[i].length <= 20
s 和 wordDict[i] 仅由小写英文字母组成
wordDict 中的所有字符串 互不相同

实现：
```go
func wordBreak(s string, wordDict []string) bool {

    // 获取s长度
    n := len(s)

    // 将wordDict转为映射
    wordMap := make(map[string]bool)

    for _, word := range wordDict {
        wordMap[word] = true
    }

    // 定义数组dp，用于存放字符串遍历匹配结果
    dp := make([]bool, n+1)
    // 第一个表示空字符串，初始化为0
    dp[0] = true

    // 从第二个字符开始遍历
    for i:=1; i<=n; i++ {
        // 对于从开始到当前字符i，通过j重新遍历，如果存在切分点j，其之前字符串存在匹配结果，且j与i之间字符串存在单词映射中存在匹配单词，则将dp[i]置为true
        for j:=0; j<i; j++ {
            if dp[j] && wordMap[s[j:i]] {
                dp[i] = true
            }
        }
    }

    // 返回字符串s的匹配结果
    return dp[n]
}
```