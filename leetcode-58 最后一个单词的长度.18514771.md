给你一个字符串s，由若干单词组成，单词前后用一些空格字符隔开。返回字符串中 最后一个单词的长度。

单词 是指仅由字母组成、不包含任何空格字符的最大子字符串。

示例 1：
> 输入：s = "Hello World"
输出：5
解释：最后一个单词是“World”，长度为 5。

示例 2：
> 输入：s = "   fly me   to   the moon  "
输出：4
解释：最后一个单词是“moon”，长度为 4。

示例 3：
> 输入：s = "luffy is still joyboy"
输出：6
解释：最后一个单词是长度为 6 的“joyboy”。

提示：
- 1 <= s.length <= 104
- s 仅有英文字母和空格 ' ' 组成
- s 中至少存在一个单词

Go实现：
```go
func lengthOfLastWord(s string) int {

	// 获取指定字符串s长度
	n := len(s)

	// 定义要返回的结果，且初始长度为0
	lastWordLength := 0

	// 从后往前进行遍历
	for i := n - 1; i >= 0; i-- {
		// 第一次遇到空格字符则跳过，遇到非空格字符则开始计算长度，等再次遇到空格字符时，如果结果长度大于0，则直接返回计算结果
		if s[i] != ' ' {
			lastWordLength++
		} else if lastWordLength > 0 {
			return lastWordLength
		}
	}

	// 结束循环，直接返回最后单词长度，可能是0，也可能是唯一单词长度
	return lastWordLength
}
```