你是一个专业的小偷，计划偷窃沿街的房屋。每间房内都藏有一定的现金，影响你偷窃的唯一制约因素就是相邻的房屋装有相互连通的防盗系统，如果两间相邻的房屋在同一晚上被小偷闯入，系统会自动报警。
给定一个代表每个房屋存放金额的非负整数数组，计算你 不触动警报装置的情况下 ，一夜之内能够偷窃到的最高金额。

示例 1：
输入：[1,2,3,1]
输出：4
解释：偷窃 1 号房屋 (金额 = 1) ，然后偷窃 3 号房屋 (金额 = 3)。
     偷窃到的最高金额 = 1 + 3 = 4 。

示例 2：
输入：[2,7,9,3,1]
输出：12
解释：偷窃 1 号房屋 (金额 = 2), 偷窃 3 号房屋 (金额 = 9)，接着偷窃 5 号房屋 (金额 = 1)。
     偷窃到的最高金额 = 2 + 9 + 1 = 12 。

提示：
1 <= nums.length <= 100
0 <= nums[i] <= 400

实现：
```go
func rob(nums []int) int {

    n := len(nums)

    // 如果只有一个元素，即表示只有一个房子，则该房子金额为最高金额
    if n == 1 {
        return nums[0]
    }

    // 如果有两个元素，则最高金额取两座房子中的较大值
    if n == 2 {
        return max(nums[0], nums[1])
    }

    // 定义化切片dp（动态数组）并初始化，用于存储不同房屋数量下可以获取的最高金额
    dp := make([]int, n)
    dp[0] = nums[0]
    dp[1] = max(nums[0], nums[1])

    // 对于房屋数量大于2的，最大金额为当前房子i和i-2房子金额之和，和i-1房子金额中的较大值者
    for i:=2; i<n; i++ {
        dp[i] = max(dp[i-2]+nums[i], dp[i-1])
    }

    // 返回所能获得最大金额
    return dp[n-1]
}

// 辅助函数，用于返回两个整数中的较大值
func max(a,b int) int {
    if a >= b {
        return a
    }
    return b
}
```