# 受控表单绑定

## 概念
- 使用React组件的状态（useState）控制表单的状态
    - React(state) > state绑定到input的value属性 > \<input/\>(value)
    - \<input/\>(value) > 把input最新的value值设置给state > React(state)

## 应用

### 准备一个value状态值
- 
```js
// 声明一个react状态 - useState
const [value, setValue] = useState('')
```

### 通过value属性绑定状态，通过onchange属性绑定状态同步的函数
- 
```js
<input
    type="text"
    // 通过value属性绑定react状态
    value={value}
    //绑定onchange事件，通过事件参数e拿到输入框最新的值，反向修改react状态
    onChange={(e) => setValue(e.target.value)}
/>
```
