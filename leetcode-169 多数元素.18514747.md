给定一个大小为 n 的数组 nums ，返回其中的多数元素。多数元素是指在数组中出现次数 大于 ⌊ n/2 ⌋ 的元素。

你可以假设数组是非空的，并且给定的数组总是存在多数元素。

示例 1：
>输入：nums = [3,2,3]
输出：3

示例 2：
>输入：nums = [2,2,1,1,1,2,2]
输出：2

提示：
- n == nums.length
- 1 <= n <= 5 * 104
- -109 <= nums[i] <= 109

给出思路，但不要代码

Go实现（摩尔计数法）：
```go
func majorityElement(nums []int) int {

	// 获取数组长度n
	n := len(nums)

	// 定义候选元素和计算器，同时候选元素初始化为数组第一个元素，相应地计数器初始化为1
	majority, count := nums[0], 1

	// 从数组第二个元素，即索引为1的位置开始遍历
	for i := 1; i < n; i++ {

		// 如果计算器为0，说明上一个候选元素已被抵消，则当前元素将重新作为候选元素，并将计数器初始为1
		if count == 0 {
			majority = nums[i]
			count = 1
		// 如果当前元素与候选元素相等，则计数器加1，反之减一
		} else if nums[i] == majority {
			count++
		} else {
			count--
		}

	}

	// 返回候选元素最后的结果
	return majority
}
```