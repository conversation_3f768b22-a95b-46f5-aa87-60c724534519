# 分清四个考察方向
- 操作技能：程序员可以借助操作手册或搜索引擎来获取的相关信息。
- 知识：是一个人认知背景和学习成果的体现，可能涵盖一些基础概念和实践常识。知识不足并不代表候选人不能胜任工作，只要掌握操作技能就可以应付工作，但是缺乏知识一定会限制个人经验和能力的提升，同样也会影响软件质量。
- 经验：经验通常和经历有关，也会影响一个人的知识结构和范围。经验还能体现一个人对知识的运用和驾驭，对做过事情的反思和总结，以及学习观察他人、与人交流的能力。
- 能力：能力是一个人做事情的态度、想法、思路、行为、方法和风格的综合表现，与知识和经验没有正相关的关系。只要具备足够的能力，拥有知识和经验就仅是一个时间问题。一个新手或许欠缺知识和经验，但这并不代表他的能力有问题；而一个老手存在知识和经验欠缺的问题，则可以说明其能力不足。
- 能力可以让一个人获得知识，知识可以让一个人更有经验，经验又会通过改变一个人的想法和思路来提高个人能力。
- 操作技能、知识和经验，只是证明应聘者能否胜任的必要条件，而不是充分条件，面试更应该关注应聘者的能力。
- 考察能力不代表要通过出题来难倒应聘者，而是要找到应聘者的亮点和长处。

# 讨厌的算法题和智力题
- 对于算法题或智力题的考察，不在于是否一定能将题目解出，更在于解题过程中的思路、方法、知识、经验，以及沟通的综合运用，忌带刁难和立威；
- 算法题考察点：
    - 在解题时是否会分解或简化问题，这体现分析能力；
    - 在解题时是否会使用一些基础知识如数据结构和算法，这体现对基础知识的掌握；
    - 讨论过程中展现的专研精神和沟通能力；
    - 心态和态度上是否有畏难情绪；
    - 解题思路及方法是否合理和先进；

# 把应聘者当成同事
-很多问题并没有标准答案，或者说同一个答案可以有多种表述，不能因为没有听到自己想要的答案就认定应聘者能力有问题。

# 向应聘者学习
- 一问一答会导致单方面的输出，形式上也显得死板；好的面试应该是一个始终在相互讨论、良性互动的过程。
- 面试的初心，是挖掘应聘者的优势，而不是证明面试官有多专业或多聪明。

# 面向综合素质的面试
对于编程能力，重点考察以下方面：
- 设计是否满足需求并能够应对可能出现的需求变化；
- 程序是否易于阅读和维护；
- 重构代码的能力如何；
- 能否测试编写的程序；

# 实习生招聘
在校生获取实习需要做的提前准备：
- 思考、总结所学知识：如果在获取知识时没有思考其含义，也难以将知识内化为自身的能力，也无法应对现实工作中多变的问题。
- 多多实践而不是只做研究：计算机工程非常依赖工程实践。
- 认识写程序的价值：编程和写作都是创作，但高质量的创作才有价值，按部就班写代码只会培养码农，需要不断反思和总结自己的代码和设计，追求精益求精。
- 不要拿教育当接口：别人给什么就学什么的被动式学习，往往是个人的选择，不能简单将被动式学习归咎于教育环境。

# 面试题解析
- 常见面试题剖析：
    - “火柴棍式” 面试题：主要考察代码逻辑了解程度。如果审题有困难，则意味着应聘者在理解用户需求及沟通上可能存在薄弱环节。
    - “火车运煤” 面试题：主要考察解题思路和表达能力。
    - 产品经理面试题：不要为开放性问题预设答案并将其作为考察面试者能力的标准。
- 重要的不是知识，而是获取知识的能力；要关注的不是问题的答案，而是解题的思路和方法。