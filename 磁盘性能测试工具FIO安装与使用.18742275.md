# 工具简介

**工具定位**

FIO 是一款专业级存储性能测试工具，可模拟服务器、硬盘等设备在不同工作压力下的表现（如数据库读写、文件传输等场景），并生成量化性能报告。

**核心优势**
- **多模式测试** - 支持模拟20+种数据读写模式（如顺序扫描大文件、随机访问小文件）。 
- **精准度量** - 提供吞吐量（MB/s）、响应速度（IOPS/每秒操作数）、延迟（毫秒级精度）三大核心指标。  
- **压力模拟** - 通过多线程/进程并发测试（可设置队列深度），真实还原高并发场景。  
- **跨平台** - 支持Windows/Linux服务器、个人电脑甚至树莓派等设备。

> 解释
> - **IOPS**：每秒完成的读写操作次数，体现存储设备的处理速度。  
> - **队列深度**：同时等待处理的I/O请求数量，数值越大压力越高。  
> - **延迟**：从发起请求到收到响应的时间，直接影响用户体验。 

# 应用场景
- **存储设备选型测试**
	- 对比SSD/HDD在不同读写比例下的性能差异（如70%读+30%写）
	- 测试NVMe硬盘在高队列深度（QD32+）时的极限吞吐量
- **系统调优验证**
	- 验证RAID阵列的条带大小设置是否合理
	- 检测文件系统（ext4/xfs）对随机小文件读写的优化效果
- **云计算性能评估**
	- 测量云硬盘的突发性能与基准性能差值
	- 验证分布式存储的IO一致性（如ceph集群）
- **异常问题排查**
	- 定位磁盘性能波动（IO抖动）的规律性特征
	- 检测硬件降级现象（如SSD闪存颗粒磨损导致的延迟突增）

> 解释
> **NVMe**：全称 Non-Volatile Memory Express​（非易失性存储器快速通道），专门为固态硬盘设计的固态硬盘（SSD）协议。
> **RAID**：​全称 Redundant Array of Independent Disks，就是把多块硬盘组合起来用，要么提速（如 RAID 0），要么备份数据（如 RAID 1），要么两者兼顾（如 RAID 5）。

# 安装与基础用法

## 安装方法

### 在线安装
```bash
# CentOS/RedHat
yum install -y libaio-devel fio

# Ubuntu/Debian
apt install -y fio
```

### 离线安装

**CentOS/RedHat 系统**

1. 联网环境下载离线包
```bash
# 下载fio及依赖
yum install yum-utils -y
yumdownloader --resolve --destdir=/tmp/fio-packages fio libaio-devel

# 打包压缩
tar czvf fio-centos.tar.gz -C /tmp/fio-packages .
```

2. 上传到目标环境进行安装
```bash
# 上传并解压
tar xzvf fio-centos.tar.gz -C /tmp/fio-packages

# 进入安装目录
cd /tmp/fio-packages

# 批量安装
rpm -Uvh --force --nodeps *.rpm
```

**Ubuntu/Debian 系统**

1. 联网环境下载离线包
```bash
# 下载fio及依赖
apt-get download $(apt-cache depends --recurse --no-recommends fio libaio1 | grep "^\w" | sort -u)

# 打包压缩
tar czvf fio-ubuntu.tar.gz *.deb
```

2. 上传到目标环境进行安装
```bash
# 上传并解压
tar xzvf fio-ubuntu.tar.gz -C /tmp/fio-packages

# 批量安装
cd /tmp/fio-packages && dpkg -i *.deb
```

**源码编译安装**

1. 官网下载源码安装包
```bash
# 下载源码包，此处以 3.39 版本为例
wget https://github.com/axboe/fio/archive/refs/tags/fio-3.39.tar.gz
```
> **官方下载**
> 官网：[http://freecode.com/projects/fio/](http://freecode.com/projects/fio/)
> github：[https://github.com/axboe/fio](https://github.com/axboe/fio)

2. 上传到目标环境进行安装
```bash
# 上传源码包到服务器
# 解压源码安装包
tar xzvf fio-3.39.tar.gz

# 进入解压目录
cd fio-fio-3.39

# 编译并安装
./configure
make && make install
```

## 命令行关键参数

| 参数                  | 参数选项                                                                                                                                                                                                                                                                                                                                                                                                                                                   | 应用场景                                                                                                                                                                                                                                                                                                                                                   | 注意事项                                                                                                                                                                                                                                                                                  |
| --------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **--rw**              | 指定I/O模式与数据分布：<br> - read/write（顺序读写）<br> - randread/randwrite（完全随机）<br> - rw/randrw（混合读写）                                                                                                                                                                                                                                                                                                                                      | **顺序读写**：测试顺序读写带宽（Throughput），适用于大块连续数据的场景（顺序I/O减少磁头移动，适合机械硬盘连续传输能力），如 机械硬盘（HDD）、视频编辑/备份 等。<br>**完全随机**：测试随机读写 IOPS 和延迟（Latency），如 SSD、数据库负载、OLTP 系统 等。<br>**混合读写**：测试读写混合负载下的性能表现，如 SSD 综合性能、企业存储系统 等。                 | * 混合读写需配合`--rwmixread=70`设置读写比例<br>* 顺序模式主要关注 带宽（MB/s） 指标，随机模式主要关注 IOPS/延迟 指标                                                                                                                                                                     |
| **--bs**              | 通 `--blocksize`，直接影响IOPS与带宽关系：<br> - 小块：更容易达到高IOPS，但吞吐量（MB/s）较低，如 --bs=4k。<br> - 大块：单次I/O传输更多数据，吞吐量高，但IOPS会显著下降，如 --bs=1m。                                                                                                                                                                                                                                                                      | **4K**：适合数据库，如 MySQL/PostgreSQL（数据库默认页大小通常为4K/8K）。<br>**16K-64K**：适合 文件系统元数据操作。<br>**1M**：适合 视频编辑/大文件传输。                                                                                                                                                                                                   | * 压测极限 IOPS：用 --bs=4k --rw=randread（最小块 + 随机读）。<br> * 压测极限吞吐：用 --bs=1m --rw=write（最大块 + 顺序写）。                                                                                                                                                             |
| **--iodepth**         | I/O 队列深度，表示每个线程（job）同时向设备提交的未完成I/O请求数量，主要用来验证压测存储设备的并行处理能力（主要为SSD）。                                                                                                                                                                                                                                                                                                                                  | **企业级 NVMe SSD**：对持续高并发&低延迟要求较高，适合深度 32-64。<br>**消费级 SSD**：对爆发性能要求高，偏间歇性负载，适合深度 16-32。<br>**云硬盘（如 EBS）**：受虚拟化层限制，适合深度 8-16。<br>**机械硬盘（HDD）**：受物理寻道时间限制，适合深度 4-8。                                                                                                 | * 使用阶梯测试法确定最佳值，如测试不同iodepth下的IOPS，观察IOPS和延迟变化，找到性能拐点。<br>* 对于 OLTP 数据库主要关注低延迟，队列深度不宜过高（如 16-32）。<br>* 对于大数据分析，可接受更高延迟换取吞吐量（如 64+）。                                                                   |
| **--numjobs**         | 指定同时运行的独立I/O线程数量，模拟真实业务中的多任务并发场景。<br>总并发公式（总未完成 I/O 请求数）= iodepth × numjobs（如 --iodepth=16 --numjobs=4 > 总并发请求数 64）。                                                                                                                                                                                                                                                                                 | **企业级NVMe SSD压测**：需多线程才能打满高性能设备的带宽和IOPS，推荐线程数 8-16。<br>**云硬盘基准测试**：避免因虚拟化层限制导致超配额或延迟波动，推荐线程数 4-8。<br>**数据库连接池模拟**：等于测试数据库连接池大小，以模拟真实并发查询（如MySQL连接池设为32，则numjobs=32）。<br>**单线程延迟测试**：排除多线程干扰，测量最基础I/O延迟，即线程数 1。      | * CPU 核数限制：numjobs 不应超过可用CPU核数（避免线程争抢CPU资源）。<br>* 与 `iodepth` 的平衡：<br> - 高 numjobs + 低 iodepth（如 32 jobs × depth 1）适合测试调度能力。<br> - 低 numjobs + 高 iodepth（如 4 jobs × depth 32）适合测试设备队列深度能力。                                   |
| **--direct**          | 绕过系统缓存（Buffer/Cache）：<br> - 1: 真实设备性能测试<br> - 0: 允许缓存加速                                                                                                                                                                                                                                                                                                                                                                             | 存储设备裸性能必设为`1`，<br>文件系统测试可设为`0`                                                                                                                                                                                                                                                                                                         | 未启用会导致测试结果虚高                                                                                                                                                                                                                                                                  |
| **--ioengine**        | 指定I/O调度引擎，即FIO如何与存储设备进行交互的引擎接口。<br>- **libaio**：Linux 原生异步I/O，内核级优化，更好满足高并发、低延迟要求，Linux服务器优先使用。<br>- **sync**：同步阻塞I/O，兼容性最好，但性能较差（每个I/O必须等上一个完成）。<br>- **posixaio**：POSIX 标准异步I/O，理论兼容性广，实际性能不如libaio。<br>- **windowsaio**：Windows 异步I/O，只能在Windows用。<br>- **io_uring**：Linux 新一代异步I/O，比libaio开销更低，但需要系统内核≥5.1。 | **libaio**：适合 企业级 SSD 压测、数据库高并发请求等（Linux 服务器）。<br>**sync**：适合 兼容性调试、磁盘基本读写能力验证 等。<br>**posixaio**：适合 测试 POSIX 标准兼容性、跨平台异步测试 等。<br>**windowsaio**：适合 Windows服务器磁盘性能测试、NTFS/ReFS 文件系统差异对比 等。<br>**io_uring**：适合 高性能设备（如Intel Optane）、磁盘极限压测 等。   | * Linux 服务器、块设备测试 优先libaio，内核≥5.1 可尝试 io_uring（需FIO编译时启用）。<br>* 调试/兼容性测试时，用 sync 排除异步模型干扰。<br>* 文件系统测试可能受 page cache 影响，加 `--direct=1` 绕过缓存。                                                                               |
| **--runtime**         | 控制测试持续时间（单位：秒）                                                                                                                                                                                                                                                                                                                                                                                                                               | **基本性能快速验证**：运行1分钟（`--runtime=60`）。<br>**稳定性/耐久性测试**：持续压测1小时（`--runtime=3600`）。<br>**生产级测试**：至少5分钟（`--runtime=300`）以上，避免瞬时性能假象。<br>**云硬盘评测**：建议30分钟（`--runtime=1800`），观察是否会出现限速。                                                                                          | * 如果设置了 `--runtime` 但没设置 `--time_based`，当fio完成指定数据量的读写时，即使没到时间也会停止。<br>* 如果同时设置 `--size` 和 `--runtime`，则哪个条件先达到就停止（即数据写完或时间到）。                                                                                           |
| **--size**            | 定义每个线程的I/O数据量：<br> - 控制测试范围<br> - 支持K/M/G单位                                                                                                                                                                                                                                                                                                                                                                                           | `--size=10G`模拟大数据处理，<br>`--size=100m`快速测试                                                                                                                                                                                                                                                                                                      | 总数据量=size×numjobs                                                                                                                                                                                                                                                                     |
| **--group_reporting** | 用于将多个并发任务（jobs）的测试结果合并输出，提供整体性能数据。                                                                                                                                                                                                                                                                                                                                                                                           | 基准测试：可以快速获取整体性能，避免逐个查看job结果。<br>云环境测试：避免产生过多日志导致存储压力。                                                                                                                                                                                                                                                        | * 必须配合 `--numjobs` 使用，单独使用无意义（单 job 无需聚合）。<br>* 聚合延迟是各 job 延迟的加权平均值，不是简单相加。<br>* 问题排查时先不加参数运行，确认无异常后再启用。                                                                                                               |
| **--filename**        | 指定性能测试的作用对象：<br> - 文件路径：如 `/data/testfile`，会受文件系统开销影响。<br> - 裸设备路径： 如 `/dev/nvme0n1`，能反映设备最真实性能。                                                                                                                                                                                                                                                                                                          | **裸设备**：如 `/dev/nvme0n1`，能消除文件系统干扰，测得设备最真实性能（如果设备已使用，会破坏设备上数据）。 <br>**文件系统**：如 `/data/testfile`，能真实反映应用场景性能（包含文件系统开销）。<br>**云硬盘**：如 `/dev/vdb`，云环境一般不允许直接访问物理设备。<br>**自动回收文件**：如 `/dev/shm/test.tmp`，一般为内存文件系统，适合测试内存盘理论极限。 | * 指定块设备路径时，fio会直接操作底层存储设备，设备上所有数据会被覆盖（包括分区表、文件系统元数据等），且过程不可逆。<br>* 测试文件路径时，Linux page cache会干扰结果，需要配合`--direct=1`参数绕过系统缓存。<br>* 直接测试裸设备时可能遇到4K/1MB对齐问题，可添加`--offset`参数确保对齐。 |
| **--offset**          | 用于精确控制I/O操作的起始点：<br> - 物理对齐：确保I/O起始地址匹配存储介质最小操作单元（4K/1MB等）。<br> - 分区跳过：避开设备前端的保留区域（如GPT分区表、RAID元数据等）。<br> - 特殊场景模拟：测试设备特定物理位置的性能特性（如SSD颗粒边缘区域）。                                                                                                                                                                                                        | **1MB对齐测试**：如企业级NVMe测试（`--offset=1m`）。<br>**4K对齐测试**：适合数据库场景（`--offset=4k`）。                                                                                                                                                                                                                                                  | * 确保 offset + size 不超过设备容量。<br>* 同时使用 `--direct=1` 确保真正的物理对齐。<br>* 新设备首次测试时，先用`--offset=0`和`--offset=4k`各跑一次对比性能差异。                                                                                                                        |
| **--lat_percentiles** | 用于统计I/O延迟百分位数:<br> - 1：表示启用，默认启用。<br> - 0：表示禁用。                                                                                                                                                                                                                                                                                                                                                                                 | 随机I/O低延迟测试：<br>- 高延迟敏感型应用​：如 etcd、MySQL 等需要稳定低延迟的存储系统。<br>- 性能基准测试​：对比不同配置或硬件的延迟分布，优化I/O调度策略。<br>- 故障排查​：通过异常百分位延迟（如P99.9突增）发现潜在问题（如磁盘故障或队列拥塞）。                                                                                                        | * `clat` 仅统计内核完成I/O的时间，`lat` 包含提交和完成的总时间。<br>* 输出可能为微秒（usec）或毫秒（msec），需结合上下文区分。                                                                                                                                                            |

# 常用测试场景

#### **全盘顺序吞吐测试**（机械硬盘/带宽验证）

```bash
fio --name=seq_write --filename=/dev/sdb --rw=write --bs=1m --direct=1 \
    --ioengine=libaio --numjobs=4 --runtime=600 --group_reporting
```
**关键配置解析**：
- `--bs=1m`：匹配机械硬盘最佳顺序传输单元，HDD的ZBR在1MB块时吞吐最优。
- `--numjobs=4`：多线程加速机械盘寻道瓶颈。
- `--runtime=600`：10分钟稳定压力测试，企业级存储需≥30分钟（即 `--runtime=1800`）。
- 强制`--direct=1`排除缓存干扰。

**关注指标**：

| 指标名称            | 指标说明                                                                | 行业基准              | 选择理由                                                     |
| ------------------- | ----------------------------------------------------------------------- | --------------------- | ------------------------------------------------------------ |
| **持续带宽**        | 全盘连续写入的平均速率：`bw_mean` in JSON输出                           | 7200RPM HDD≥150MB/s   | 验证硬盘物理传输极限                                         |
| **带宽波动率**      | (最大带宽 - 最小带宽)/平均带宽 ×100%：`(bw_max - bw_min)/bw_mean ×100%` | <5%（企业级存储要求） | 检测机械盘磁头寻道稳定性（波动>10%可能存在坏道或固件问题）   |
| **尾部延迟(99th%)** | 99%请求的完成时间 ≤ 该值                                                | <50ms（机械盘标准）   | 识别突发负载下的性能瓶颈（如磁头复位延迟）                   |
| **缓存干扰率**      | (开启缓存带宽 - 直写带宽)/直写带宽 ×100%                                | <10%                  | 验证`direct=1`参数有效性（差值过大说明测试被Page Cache干扰） |

#### **数据库随机IOPS测试**（SSD/OLTP场景）
```bash
fio --name=oltp_sim --filename=/mnt/ssd/testfile --rw=randrw --rwmixread=70 \
    --bs=4k --iodepth=32 --numjobs=8 --runtime=300 --direct=1 --group_reporting
```
**关键配置解析**：
- `--bs=4k`：匹配数据库页大小（以数据库实际页配置为准）。
- `--rwmixread=70`：表示70%读+30%写的事务负载模拟，如银行核心系统70/30。
- `--iodepth=32`：NVMe SSD的最佳QD（Queue Depth）。
- `--numjobs=8`：模拟8个应用线程并发，实际总队列深度为 32*8 > 256，每vCPU对应1job。
- `--filename`：测试文件系统实际性能。

**关注指标**

| 指标名称              | 指标说明                                       | 行业基准（金融） | 选择理由                                                         |
| --------------------- | ---------------------------------------------- | ---------------- | ---------------------------------------------------------------- |
| **平均IOPS**          | 每秒完成的总I/O操作数：`iops_mean`             | 依SSD型号而定    | 基础性能能力（如三星PM1735需≥800K）                              |
| **尾部延迟(99.9th%)** | 99.9%请求的完成时间 ≤ 该值                     | <20ms（金融级）  | 保障核心交易事务响应时间（超时可能触发数据库锁等待）             |
| **读写延迟比**        | 写操作平均延迟 / 读操作平均延迟：`r_lat/w_lat` | 1:1.2~1.5        | 检测SSD写放大效应（比值>1.5可能需优化FTL算法）                   |
| **QoS稳定性**         | 30秒窗口内IOPS标准差 / 平均IOPS ×100%          | <3%              | 确保企业级SSD在持续压力下表现稳定（波动>5%可能触发存储阵列降级） |

#### **云硬盘极限压力测试**（队列深度验证）
```bash
fio --name=cloud_stress --rw=randread --bs=4k --iodepth=64 \
    --ioengine=libaio --numjobs=16 --time_based --runtime=1800 \
    --filename=/dev/vdc --direct=1 --output=cloud_result.json
```
**关键配置解析**
- `--time_based`：强制按时长运行（即使提前完成数据量）。
- `--output=cloud_result.json`：生成结构化日志，便于自动化分析。
- `--runtime=1800`：持续30分钟，模拟云硬盘的burst周期（AWS gp3=30分钟），检测云盘性能衰减，以通过 `slat`/`clat` 百分比变化检测限流。

**性能验证**：
- 基础性能：`--iodepth=1`（单队列）
- 峰值性能：`--iodepth=32`（突发桶）
- 稳定性：`--time_based --runtime=86400`（24小时）

**关注指标**

| 指标名称             | 指标说明                                         | 行业基准（AWS）            | 选择理由                                                   |
| -------------------- | ------------------------------------------------ | -------------------------- | ---------------------------------------------------------- |
| **突发性能持续时间** | 从测试开始到性能下降至基础水平的时间             | ≥30分钟（gp3需维持30分钟） | 验证云盘burst credits机制有效性（短于承诺时长可能违反SLA） |
| **基础性能衰减率**   | (初始30分钟IOPS - 最终30分钟IOPS)/初始IOPS ×100% | <15%                       | 检测底层超售导致的限流（衰减>20%应触发云厂商扩容）         |
| **延迟尖峰密度**     | 延迟超过100ms的请求数占比                        | <0.01%                     | 识别虚拟化层调度干扰（频繁尖峰可能需调整实例类型）         |
| **带宽成本比**       | `(实测带宽/购买带宽)×100%`                       | 应≥90%                     | 验证云厂商承诺性能                                         |

#### **元数据性能测试**（小文件场景）
```bash
fio --name=meta_test --rw=randwrite --bs=512b --iodepth=1 \
    --numjobs=128 --direct=1 --size=8m --nrfiles=128 \
    --directory=/testdir --fsync=1 --group_reporting \
    --time_based --runtime=120
```
**关键配置解析**：
- `--bs=512b`：极端小块测试 inode分配+data block分配 效率。
- `--numjobs=128`：高并发模拟海量小文件写入。
- `--size=8m`：每个线程写入8MB（总数据量 8m×128=1GB，可控）。
- `--nrfiles=128`：每个线程创建独立文件（生成128个文件）。
- `--directory=/testdir`：指定独立目录避免文件覆盖。
- `--fsync=1`：每次写操作后触发元数据提交。
- `--runtime=120`：限制测试时长为2分钟，避免失控。

**关注指标**

| 指标名称             | 指标说明                                                 | 行业基准（Ext4） | 选择理由                                                                  |
| -------------------- | -------------------------------------------------------- | ---------------- | ------------------------------------------------------------------------- |
| **文件创建吞吐**     | 每秒成功创建的文件数：`vfs_create`调用次数               | ≥5000 ops/s      | 验证目录项(dentry)缓存效率（低于3000 ops/s可能需调整max_dir_size）        |
| **fsync提交延迟**    | 数据从内存同步到磁盘的耗时：`jbd2`日志提交延迟           | 99th% <10ms      | 检测日志文件系统(journal)性能（需配合`--fsync=1` ）                       |
| **inode分配延迟**    | 从inode空闲表分配新inode的平均时间：`ext4_new_inode`耗时 | <5μs             | 反映存储碎片化程度（>10μs需运行e4defrag）                                 |
| **目录项缓存命中率** | 从内存dentry缓存获取目录项的概率                         | >95%             | 优化文件查找性能（读取`/proc/sys/fs/dentry`，命中率<90%需增大dentry缓存） |

## 测试结果解读

#### 关键指标

**IOPS**
- **定义**：每秒处理多少次I/O操作。
- **示例**：IOPS=11.3k 表示每秒处理11300次读写。
- **关键点**：
  - 机械硬盘IOPS≈200，SSD可达数万。
  - 高IOPS不一定等于高效。

**带宽BW**
- **定义**：每秒传输的数据量。
- **示例**：BW=43.0MiB/s 表示每秒传输43兆字节。
- **换算关系**：BW = IOPS × 块大小（如4k块达到43MiB/s需要：43*1024/4 ≈ 11k IOPS）。

**延迟latency**
- **分层理解**：
  - **slat（提交延迟）**：系统处理请求的时间。
  - **clat（完成延迟）**：设备实际处理时间。
  - **lat（总延迟）**：slat + clat。

#### 指标分层管理

- 第一层：基础性能（IOPS/带宽）
- 第二层：稳定性（QoS/衰减率）
- 第三层：业务合规（尾部延迟/SLA）