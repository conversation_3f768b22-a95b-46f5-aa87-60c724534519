# Abstract 摘要

The recent success of large vision language models shows great potential in driving the agent system operating on user interfaces. 
大型视觉语言模型最近的成功展示，驱动代理系统在用户界面上进行操作具有巨大的潜力。

However, we argue that the power multimodal models like GPT-4V as a general agent on multiple operating systems across different applications is largely underestimated due to the lack of a robust screen parsing technique capable of: 
1、reliably identifying interactable icons within the user interface, and 
2、understanding the semantics of various elements in a screenshot and accurately associate the intended action with the corresponding region on the screen. 
然而，我们认为，由于缺乏有效的屏幕解析技术，像GPT-4V这样的多模态大模型，作为跨应用程序的多系统通用代理在很大程序上被低估。这种技术能够：
1、可靠地识别用户界面内的可交互图标，并
2、理解屏幕截图内不同元素的语义，并准确地将用户预期行为与屏幕相应区域关联起来。

To fill these gaps, we introduce OMNIPARSER, a comprehensive method for parsing user interface screenshots into structured elements, which significantly enhances the ability of GPT-4V to generate actions that can be accurately grounded in the corresponding regions of the interface. 

We first curated an interactable icon detection dataset using popular webpages and an icon description dataset. 

These datasets were utilized to fine-tune specialized models: a detection model to parse interactable regions on the screen and a caption model to extract the functional semantics of the detected elements. 

OMNIPARSER significantly improves GPT-4V's performance on ScreenSpot benchmark. And on Mind2Web and AITW benchmark, OMNIPARSER with screenshot only input outperforms the GPT-4V baselines requiring additional information outside of screenshot。

![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119103723385-78784269.png)
![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119103736517-884903599.png)
![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119103756150-1822634509.png)

Examples of parsed screenshot image and local semantics by OmniParser. The inputs to OmniParse are user task and UI screenshot, from which it will produce: 1. parsed screenshot image with bounding boxes and numeric IDs overlayed, and 2. local semantics contains both text extracted and icon description.

# Curated Dataset for Interactable Region Detection and Icon Functionality Description

We curate a dataset of interactable icon detection dataset, containing 67k unique screenshot images, each labeled with bounding boxes of interactable icons derived from DOM tree. We first took a 100k uniform sample of popular publicly availabe urls on the clueweb dataset, and collect bounding boxes of interactable regions of the webpage from the DOM tree of each urls. We also collected 7k icon-description pairs for finetuning the caption model.
![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119103905300-74446648.png)

**Examples from the Interactable Region Detection dataset.** . TThe bounding boxes are based on the interactable region extracted from the DOM tree of the webpage.

# Results
We evaluate our model on SeeClick, Mind2Web, and AITW benchmarks. We show that our model outperforms the GPT-4V baseline on all benchmarks. We also show that our model with screenshot only input outperforms the GPT-4V baselines requiring additional information outside of screenshot.
![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119104119093-184133227.png)
![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119104127821-1763859315.png)
![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119104135453-1242917501.png)

# Plugin-ready for Other Vision Language Models
To further demonstrate OmniParser is a plugin choice for off-the-shelf vision langauge models, we show the performance of OmniParser combined with recently announced vision language models: Phi-3.5-V and Llama-3.2-V. As seen in table, our finetuned interactable region detection (ID) model significantly improves the task performance compared to grounding dino model (w.o. ID) with local semantics across all subcategories for GPT-4V, Phi-3.5-V and Llama-3.2-V. In addition, the local semantics of icon functionality helps significantly with the performance for every vision language model. In the table, LS is short for local semantics of icon functionality, ID is short for the interactable region detection model we finetune. The setting w.o. ID means we replace the ID model with original Grounding DINO model not finetuned on our data, and with local semantics. The setting w.o. ID and w.o LS means we use Grounding DINO model, and further without using the icon description in the text prompt.
![image](https://img2024.cnblogs.com/blog/1548975/202411/1548975-20241119104225748-413487756.png)

# Demo of Mind2Web Tasks
[demo1](https://microsoft.github.io/OmniParser/static/videos/web_5_demo_nocap.mp4)
[demo2](https://microsoft.github.io/OmniParser/static/videos/web_22_demo_nocap.mp4)

# Citation
```bash
@misc{lu2024omniparserpurevisionbased,
      title={OmniParser for Pure Vision Based GUI Agent}, 
      author={Yadong Lu and Jianwei Yang and Yelong Shen and Ahmed Awadallah},
      year={2024},
      eprint={2408.00203},
      archivePrefix={arXiv},
      primaryClass={cs.CV},
      url={https://arxiv.org/abs/2408.00203}, 
}
```