## 题目

给你字符串 `s` 和整数 `k` 。
请返回字符串 `s` 中长度为 `k` 的单个子字符串中可能包含的最大元音字母数。
英文中的 元音字母 为（`a`, `e`, `i`, `o`, `u`）。

示例 1：

> 输入：s = "abciiidef", k = 3
> 输出：3
> 解释：子字符串 "iii" 包含 3 个元音字母。

示例 2：

> 输入：s = "aeiou", k = 2
> 输出：2
> 解释：任意长度为 2 的子字符串都包含 2 个元音字母。

示例 3：

> 输入：s = "leetcode", k = 3
> 输出：2
> 解释："lee"、"eet" 和 "ode" 都包含 2 个元音字母。

示例 4：

> 输入：s = "rhythms", k = 4
> 输出：0
> 解释：字符串 s 中不含任何元音字母。

示例 5：

> 输入：s = "tryhard", k = 4
> 输出：1

提示：

- `1 <= s.length <= 10^5`
- `s` 由小写英文字母组成
- `1 <= k <= s.length`

## go 实现示例

```go
func maxVowels(s string, k int) int {

	// 定义当前窗口的元音字母数
	currentCount := 0

	// 计算第一个窗口的元音字母数
	for _, c := range s[:k] {
		if isVowel(byte(c)) {
			currentCount++
		}
	}

    // 定义最大元音字母数
	maxCount := currentCount

	// 如果第一个窗口的元音字母数已经等于k，则直接返回
	if maxCount == k {
		return maxCount
	}

	// 开始滑动窗口
	for i:=k; i<len(s); i++ {

		// 如果新加入的字母是元音字母，则元音字母数加1
		if isVowel(byte(s[i])) {
			currentCount++
		}

		// 如果移除的字母是元音字母，则元音字母数减1
		if isVowel(byte(s[i-k])) {
			currentCount--
		}

		// 更新最大元音字母数
		if currentCount > maxCount {
			maxCount = currentCount
		}

	}

	// 返回最大元音字母数
	return maxCount
}

func isVowel(c byte) bool {
	return c == 'a' || c == 'e' || c == 'i' || c == 'o' || c == 'u'
}
```
