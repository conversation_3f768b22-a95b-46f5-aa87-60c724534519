## 前置准备
检查用户：root

## 使用 `lsblk`

命令作用：列出块设备基本信息，并指定输出 `name`、`rota` 列。

```bash
# -d：仅显示磁盘设备本身​（不显示分区信息）。
# -o name,rota：自定义输出列，仅显示 ​设备名称（name）​​ 和 ​旋转属性（rota）​。
# 
lsblk -d -o name,rota
```
 输出中 `rota=0` 表示SSD，`rota=1` 表示HDD。

## 使用 `udevadm`

命令作用：查询 ​udev 数据库，获取指定设备（如 `/dev/sda` ）详细信息（属性、规则、路径等）。

```bash
# --query=property：指定查询类型为​设备属性。
# --name=/dev/sda：指定要查询的设备名称。
udevadm info --query=property --name=/dev/sda | grep ROTA
```
输出中 `ROTATIONAL` 为0则表示为SSD，为1则为HDD。

## 检查 `/sys/block`

直接读取内核提供的磁盘属性（如 `/dev/sda` ）。
```bash
cat /sys/block/sda/queue/rotational
```
输出 `0` 表示SSD，输出 `1` 则为HDD。

## 使用 `smartctl`（需单独安装）

命令作用：检测指定磁盘信息。

```bash
# 安装 smartmontools
yum install smartmontools -y

# 检查磁盘类型
# -a /dev/sda：显示 /dev/sda 磁盘详细信息。
smartctl -a /dev/sda | grep "Rotation Rate"
```
输出显示带 `Solid State Device` 则为SSD，带具体转速的则为HDD（如 `7200 rpm`）。

## 使用 `hdparm`（需单独安装）

命令作用：检测指定磁盘信息。

```bash
# 安装 hdparm
yum install hdparm -y

# 检查磁盘类型
# -I /dev/sda：显示 /dev/sda 磁盘详细信息。
hdparm -I /dev/sda | grep "Nominal Media Rotation Rate"
```
输出显示带 `Solid State Device` 则为SSD，带具体转速的则为HDD（如 `7200`）。

## 使用 `lshw`（需单独安装）

命令作用：列出系统中所有的磁盘设备及详细信息。

```bash
# 安装 lshw
yum install lshw -y

# 检查磁盘信息
# -class disk：限制输出仅显示磁盘类别的硬件设备。
lshw -class disk
```
输出中 `description` 或 `product` 如果包含 `SSD` 则为SSD，反之则为 HDD。