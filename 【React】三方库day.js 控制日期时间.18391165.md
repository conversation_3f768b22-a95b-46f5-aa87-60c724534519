# day.js

## 介绍
- Day.js是一个极简的JavaScript库，可以为现代浏览器解析、验证、操作和显示日期和时间。

## 使用

### 官网
- [https://dayjs.fenxianglu.cn](https://dayjs.fenxianglu.cn)

### 安装
- `npm install dayjs`

### 应用

#### [格式化](https://dayjs.fenxianglu.cn/category/display.html#%E6%A0%BC%E5%BC%8F%E5%8C%96)
- 根据传入的占位符返回格式化后的日期
- 
```js
dayjs().format() 
// 默认返回的是 ISO8601 格式字符串 '2020-04-02T08:02:17-05:00'

dayjs('2019-01-25').format('[YYYYescape] YYYY-MM-DDTHH:mm:ssZ[Z]') 
// 'YYYYescape 2019-01-25T00:00:00-02:00Z'

dayjs('2019-01-25').format('DD/MM/YYYY') // '25/01/2019'
```