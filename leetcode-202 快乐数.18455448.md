编写一个算法来判断一个数 n 是不是快乐数。

「快乐数」 定义为：
对于一个正整数，每一次将该数替换为它每个位置上的数字的平方和。
然后重复这个过程直到这个数变为 1，也可能是 无限循环 但始终变不到 1。
如果这个过程 结果为 1，那么这个数就是快乐数。
如果 n 是 快乐数 就返回 true ；不是，则返回 false 。

示例 1：
输入：n = 19
输出：true
解释：
12 + 92 = 82
82 + 22 = 68
62 + 82 = 100
12 + 02 + 02 = 1

示例 2：
输入：n = 2
输出：false

提示：
1 <= n <= 231 - 1

实现：
```go
func isHappy(n int) bool {

    // 初始化快慢指针slow和fast，初始值为n和n第一次位数平方和
    slow, fast := n, getNumSquare(n)

    // 当指针fast不等于1以及不等于slow时，各自进行位数平方和的替换
    for fast != 1 && slow != fast {
        // 每一次循环，slow替换一次（相当于走一步），fast替换两次（相当于走两步）
        slow = getNumSquare(slow)
        fast = getNumSquare(getNumSquare(fast))
        // 如果 slow 等于 fast，说明进入了循环链表，已无法变成1，故返回false
        if slow == fast {
            return false
        }
    }

    return true
}

// 求数字位数平方和
func getNumSquare(n int) int {

    //  定义位数平方和
    sum := 0

    // 当数字n大于0时，对10取余，并累积余数平方，直到n不大于0
    for n > 0 {
        digit := n % 10
        sum += digit * digit
        n /= 10
    }

    // 返回累积结果
    return sum
}
```