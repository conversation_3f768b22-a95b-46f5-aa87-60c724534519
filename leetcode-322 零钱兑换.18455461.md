给定一个包含非负整数的 m x n 网格 grid ，请找出一条从左上角到右下角的路径，使得路径上的数字总和为最小。
说明：每次只能向下或者向右移动一步。

示例 1：
![image](https://img2024.cnblogs.com/blog/1548975/202410/1548975-20241010002509065-592963701.png)
输入：grid = [[1,3,1],[1,5,1],[4,2,1]]
输出：7
解释：因为路径 1→3→1→1→1 的总和最小。

示例 2：
输入：grid = [[1,2,3],[4,5,6]]
输出：12

提示：
m == grid.length
n == grid[i].length
1 <= m, n <= 200
0 <= grid[i][j] <= 200

实现：
```go
func minPathSum(grid [][]int) int {

	// 获取矩阵grid长度和宽度
    m := len(grid)
	n := len(grid[0])

	// 定义dp，用于存储grid各个节点最短路径和
    dp := make([][]int, m)

    // 初始化dp第一列，注意除第一行节点[0][0]dp是与grid一致外，其他节点均为与上一行的路径和
	for i := 0; i < m; i++ {
        dp[i] = make([]int, n)
		dp[i][0] = grid[i][0]
        if i > 0 {
            dp[i][0] += dp[i-1][0]
        }
	}

    // 初始化dp第一行，注意除第一行节点[0][0]dp是与grid一致外，其他节点均为与左节点的路径和
    for j:=1; j<n; j++ {
        dp[0][j] = dp[0][j-1]+grid[0][j]
    }

	// 对其他节点最短路径和进行遍历计算
    for i := 1; i < m; i++ {
		for j := 1; j < n; j++ {
			dp[i][j] = min(dp[i-1][j], dp[i][j-1]) + grid[i][j]
		}
	}

	// 返回右下角节点最短路径
    return dp[m-1][n-1]

}

// 辅助函数，用于返回两数之中的较小值
func min(a, b int) int {
	if a <= b {
		return a
	}
	return b
}
```