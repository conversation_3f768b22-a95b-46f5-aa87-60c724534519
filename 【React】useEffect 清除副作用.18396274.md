# useEffect 清除副作用

## 介绍
- 在 useEffect 中编写的**由渲染本身引起的对接组件外部的操作**，社区也经常把它叫做副作用操作，比如在useEffect中开启了一个定时器，我们想在组件卸载时把这个定时器再清理掉，这个过程就是清理副作用。

## 说明
- 清除副作用的函数最常见的执行时机是在组件卸载时自动执行。

## 需求
### 在son组件渲染时开启一个定时器，卸载时清除这个定时器。
###
-  
```js
import { useEffect, useState } from "react"

function Son() {

  // 渲染时开启一个定时器
  useEffect(() => {
    const timer = setInterval(() => {
      console.log("定时器执行中...")
    }, 1000)

    return () => {
      // 清除副作用（组件卸载时）
      clearInterval(timer)
    }
  }, [])

  return <div>this is Son</div>
}
function App() {

  // 通过条件渲染模拟组件卸载
  const [show, setShow] = useState(true)
  return (
    <div>
      {show && <Son />}
      <button onClick={() => setShow(false)}>卸载son组件</button>
    </div>
  );
}

export default App;

```