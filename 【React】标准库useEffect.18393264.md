# useEffect

## 介绍
- 是一个 react hook函数，用于在react组件中创建不是由事件引起而是由渲染本身引起的操作，比如发送ajax请求，更改dom等等。
- ![image](https://img2024.cnblogs.com/blog/1548975/202409/1548975-20240902185547940-956666175.png)
- 说明：上面的组件中没有发生任何的用户事件，组件渲染完毕之后就需要和服务器要数据，整个过程属于 “只由渲染引起的操作”

## 基础使用

### 需求
- 在组件渲染完毕之后，立刻从服务端获取频道列表式数据并显示到页面中

### 语法
- 格式：`useEffect( () => { }, [])`
- 参数1：是一个函数，叫做副作用函数，在函数内部可以放置要执行的操作。
- 参数2：是一个数组（可选参），在数组里放置依赖项，不同依赖项会影响第一个参数函数的执行，当是一个空数组的时候，副作用函数只会在zujian 渲染完毕之后执行一次。

### 示例
- 
```js
import { useEffect, useState } from "react"

const URL = 'http://geek.itheima.net/v1_0/channels'

function App() {

  // 创建一个状态数据
  const [list, setList] = useState([])

  useEffect(() => {
    // 额外的操作，获取频道列表
    async function getList() {
      const res = await fetch(URL)
      const jsonRes = await res.json()
      console.log(jsonRes)
      setList(jsonRes.data.channels)
    }
    getList()
  }, [])

  return (
    <div>
      this is App
      <ul>
        {list.map(item => <li key={item.id}>{item.name}</li>)}
      </ul>
    </div>
  );
}

export default App;
```