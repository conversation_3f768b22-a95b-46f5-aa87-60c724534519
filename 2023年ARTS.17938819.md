# ARTS打卡第20周
>A: Algorithm 一道算法题

leetcode-13
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17929625.html
>T: Technique/Tips 分享一个小技术

ansible可以在yml中通过关键字register，来捕获指令执行的结果，，以便后续的使用，示例如图
```yml
- hosts: "g1"
  tasks:
    - name: "执行脚本测试"
      shell: sh /app/handcheck/status.sh
      register: result
    - name: "检查结果"
      fail: msg: "{{result.stdout_lines}}"
      failed_when: result.rc != 0
```
>S: Share 分享一个观点

学海无涯，没有业务落地推动的技术研究，很容易竹篮打水一场空，或者是半途而废，所以个人副业发展的规划，也要以切实的用户需求为锚点，从满足小需求，到逐渐满足大需求。

# ARTS打卡第19周
>A: Algorithm 一道算法题

leetcode-274
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17921525.html
>T: Technique/Tips 分享一个小技术

在执行需要输入交互提示的bash脚本时，只需要通过 <<< 传入要输入的字符即可，例如 `sh check.sh <<< y`
>S: Share 分享一个观点

读到大学，如果去菜场卖菜，是书白读了吗？要是不愿意去，是脱不掉孔乙己的长衫吗？在直播带货镜头前卖农场品，本质上和菜场卖菜有多大区别？打破 “万般皆下品，惟有读书高” 的刻板印象，直面人生的打磨和检验，何尝不也是一种境界。

# ARTS打卡第18周
>A: Algorithm 一道算法题

leetcode-238
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17924970.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/p/17896180.html
>S: Share 分享一个观点

AI时代，AI懂的更多，会的更多，我们还需要学习吗？

AI时代，批判性思维和创造力的重要性超过了传统的事实性记忆，但这并不意味着事实性知识的重要性下降了，因为批判性思维和创造力是建立在大量事实性知识基础之上的；
我们所涉猎的知识越多，了解得越深，眼界就越远，视野和思考的维度就越广，对世界的认知程度，决定了我们能够达到的认知水平；
在AI时代，我们当前的能力差异会被放大，强者会变得更强，而弱者则可能会被甩在后面，这就是为什么我们需要不断地学习和提升自己，以适应这个快速变化的时代。

# ARTS打卡第17周
>A: Algorithm 一道算法题

leetcode-122
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17878576.html
>T: Technique/Tips 分享一个小技术

windows也能直接在命令行终端使用ssh协议进行ssh连接。
>S: Share 分享一个观点

leetcode-55

# ARTS打卡第16周
>A: Algorithm 一道算法题

leetcode-169
>R: Review 读一篇英文文章

https://www.english-for-students.com/seven-things-to-be-happy.html
>T: Technique/Tips 分享一个小技术

ansible常用模块：

核心模块：
command: 在远程主机上执行命令。
shell: 在远程主机上执行Shell命令。
copy: 将文件复制到远程主机。
file: 管理文件和目录。
template: 使用Jinja2模板引擎创建文件。
yum、apt、dnf: 管理包。
系统管理模块：
user、group: 管理用户和用户组。
service: 管理系统服务。
cron: 管理定时任务。

网络模块：
ios_command、ios_config、nxos_command、nxos_config: 用于Cisco设备的管理。
bigip_command、bigip_config: 用于F5 BIG-IP的管理。
nmcli: 用于NetworkManager的管理。

云服务模块：
ec2: AWS EC2实例的创建和管理。
azure_rm: Azure资源管理器的模块。
gcp: Google Cloud Platform的模块。

数据库模块：
mysql_db、postgresql_db: 数据库的管理。

容器和编排模块：
docker_image、docker_container: Docker容器的管理。

k8s: Kubernetes的模块。

安全和身份验证模块：
openssl_certificate: 管理SSL证书。
htpasswd: 管理Apache HTTP Server密码文件。

其他模块：
git: 从Git仓库拉取代码。
debug: 用于调试和输出调试信息。

>S: Share 分享一个观点

# ARTS打卡第15周
>A: Algorithm 一道算法题

leetcode-26
>R: Review 读一篇英文文章

https://language.chinadaily.com.cn/a/202311/23/WS655edd74a31090682a5efb4a.html
>T: Technique/Tips 分享一个小技术

bash常用特殊变量：
`$0`：脚本的名称，即命令行上使用的脚本的名称。
`$1, $2, $3...`：表示传递给脚本的位置参数，`$1`是第一个参数，`$2`是第二个参数，以此类推。
`$#`：表示传递给脚本的参数的总数。
`$@`：表示所有传递给脚本的参数的列表。
>S: Share 分享一个观点

# ARTS打卡第14周
>A: Algorithm 一道算法题

leetcode-58
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17933867.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/articles/17763513.html
>S: Share 分享一个观点

对于AI技术发展汹涌的趋势，很多人对其可能存在的不可控有着深深的担忧，但担忧对于趋势的到来无补于事，所以如果怎样都无法阻止其发展，那就干脆加速这个过程，提前把问题都暴露出来，不管未来可能会出现怎样难以处理的问题，对于已知问题解决方案的探索，也远胜于内心一直处于对未知恐惧的煎熬。

# ARTS打卡第13周
>A: Algorithm 一道算法题

leetcode-27
>R: Review 读一篇英文文章

https://language.chinadaily.com.cn/a/202311/10/WS654deec2a31090682a5ed9e1.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/p/17816275.html
>S: Share 分享一个观点

https://testerhome.com/topics/38187

# ARTS打卡第12周
>A: Algorithm 一道算法题

leetcode-88
>R: Review 读一篇英文文章

https://language.chinadaily.com.cn/a/202311/01/WS6542172aa31090682a5ebea1.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/p/17771889.html
>S: Share 分享一个观点

为什么说现在的趋势是大公司在逐渐解构，然后超级个体在形成？

技术创新和互联网的发展：技术的飞速发展，特别是移动互联网、视频电商和人工智能的崛起，使得个体有了更多的机会和平台去展示自己，发挥自己的价值。这些技术让个体能够更容易地接触到大量的信息和资源，提高了个体的生产效率和创新能力；
组织形态的变化：随着技术的发展，组织形态也在发生变化。比如分布式组织、项目制组织、混序组织、矩阵组织、平台+团队的生态型组织等等。这些新的组织形态使得个体有了更多的自主权和发展空间；
个体能的提升：在这个时代，个体需要具备一项专业技能，并打造自己的个人IP，具备一定的知名度和影响力。这就需要个体不断地学习和提升自己，以适应快速变化的环境；
社会环境的变化：在过去，一个人想要成功，可能需要依附于一家公司，一个企业。而现在，自媒体的兴起，让很多“个人”成为了一家极具价值的公司和企业。

# ARTS打卡第11周
>A: Algorithm 一道算法题

leetcode-14
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17937031
>T: Technique/Tips 分享一个小技术

在shell中，IFS（Internal Field Separator，内部字段分隔符）是一个特殊的环境变量，用于定义命令和变量的分隔符。默认情况下，IFS的值是空格、Tab键和换行符。以下是一个使用IFS的示例：
```bash
#!/bin/bash
IFS="."
str="***********"
for s in $str
do
    echo $s
done
```
>S: Share 分享一个观点

种一棵树最好的时机是10年前，其次是现在。

# ARTS打卡第10周

>A: Algorithm 一道算法题

leetcode-392
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17770878.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/p/17771575.html
>S: Share 分享一个观点

现在常说的产能过剩，很多是站在国家宏观调控的层面，但站在我们日常生活的角度，发现很多事情也是在反映着这个事实，当想在购物平台上搜索需要的商品时，经常会出来很多选择，看上去功能都很丰富，很齐全，虽然这对一些人看来很容易陷入选择困难，但另一个角度看，确实也是在享受着产能提升的便利。

但当很多基本功能都可以满足时，如果大家产品都是趋同，那就很容易陷入同质化竞争，利润不断被摊薄，甚至陷入恶性竞争，但与此同时，也总有些人能推陈出新，走出内卷，进入良性循环，然后进一步观察，就会发现这些走出来的产品，除了在基础功能满足外，总是能聚焦某些用户体验的关注点进行细分和挖掘，提供额外的东西如情绪价值，像董宇辉，李子柒等。

这个给到我的启示，很多围绕商品的规律，除了可以应用在那些看得见摸得着的物品，提供的劳务性或知识性的人工服务之外，也可以用来经营我们自己的职业发展，不管是职场和自由工作者，虽然不同工作的产出会以各种形式，但除了提供本职工作的价值外，还可以在围绕本职产出的基础上，进一步挖掘附加价值。

比如医生给病人看病是本职，然后向外分享自己的工作总结或体会，让更多人提升自己日常保健的意识和能力；比如保险销售，推销公司保险产品是本职，然后向外科普保险要怎么选择更合适自己，出险时协助客户理赔时一些相关视角的见闻，都是更容易让人接受以及提升信任度的，反过来也能提升本职的业绩；比如程序员，开发和维护业务需求的系统功能是本职，分享工作过程积累的技术经验和见解，帮助更多人提升职业技能，开发一些感兴趣的小应用或小游戏，精进自身技能的同时，也挖掘未来发展更多可能性（看有没人愿意使用或喜欢）等。
# ARTS打卡第9周

>A: Algorithm 一道算法题

leetcode-334
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17758446.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/articles/17732521.html
>S: Share 分享一个观点

https://testerhome.com/topics/37632

# ARTS打卡第8周
> A: Algorithm 一道算法题

leetcode-283
> R: Review 读一篇英文文章

I believe there is a person who brings sunshine into your life. That person may have enough to spread around. But if you really have to wait for someone to bring you the sun and give you a good feeling, then you may have to wait a long time.
我相信会有一个人会给你的生活带来阳光。这个人也会有足够的东西可以传播。但如果你真的要等某人给你带来阳光，给你带来美好的感受，那你也需需要登上很长时间。
> T: Technique/Tips 分享一个小技术

nmcli常见用法：
1. 查看网络状态：`nmcli networking`，显式网络是否启用；
2. 查看设备信息：`nmcli device`，显示所有设备的状态和信息；
3. 查看连接信息：`nmcli connection show`，显示所有网络连接的状态和信息；
4. 查看特定网络连接设置：例如 `nmcli connection show eth0`
5. 查看特定接口属性：`nmcli device show eth0`
> S: Share 分享一个观点

在知识经济时代，怎么从工业化社会大分工的习惯和思维中转变过来，是每个人都要面对的课题，知识经济时代渐渐不再是所有人一套卷子，而是每个人都要给自己出卷子，然后自己作答，因为签上的是自己的名字。

# ARTS打卡第7周
> A: Algorithm 一道算法题

leetcode-28
> R: Review 读一篇英文文章

In the flood of darkness, hope is the light. It brings comfort, faith, and confidence. It gives us guidance when we are lost, and gives support when we are afraid. And the moment we give up hope, we give up our lives. The world we live in is disintegrating into a place of malice and hatred, where we need hope and find it harder. In this world of fear, hope to find better, but easier said than done, the more meaningful life of faith will make life meaningful.
在黑暗的洪流中，希望是一种光明。它带来了安慰，信念和信心。它在我们迷失的时候给予了指引，在我们害怕的时候给予了支持。在我们放弃希望的时候，我们也放弃了生命。我们生存的世界正在瓦解成充满恶意和仇恨的地方，在那里我们更需要希望，但发现要实现它却很困难。在这个充满恐惧的世界，找到希望谈何容易。对更好，更有意义生活的信仰会让生命更有意义。
> T: Technique/Tips 分享一个小技术

网络掩码，也称为子网掩码或地址掩码，是一种用于确定IP地址中哪些位表示网络地址（即子网）和哪些位表示主机地址的位掩码。网络掩码是32位的，与IP地址结合使用，通过逻辑运算将IP地址划分为网络标识（Net.ID）和主机标识（Host.ID）。网络掩码由连续的二进制1和0组成，其中1表示网络地址部分，0表示主机地址部分。
例如，对于IP地址*************和子网掩码*************，二进制形式下的子网掩码会将IP地址的前24位遮掩，只留下后8位用于主机标识。这意味着***********到*************的地址都属于同一个子网。通过子网掩码，可以确定两台计算机是否属于同一网段，这是通过将IP地址和子网掩码进行二进制“与”运算来实现的。
> S: Share 分享一个观点

要做可以改行的人，不要做不能改行的马。

# ARTS打卡第6周
>A: Algorithm 一道算法题

https://leetcode.cn/problems/reverse-words-in-a-string/?envType=study-plan-v2&envId=leetcode-75
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17713828.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/articles/17720545.html
>S: Share 分享一个观点

# ARTS打卡第5周
>A: Algorithm 一道算法题

leetcode-345
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17702944.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/articles/17709986.html
>S: Share 分享一个观点

流水不争先，争的是滔滔不绝

# ARTS打卡第4周
>A: Algorithm 一道算法题

leetcode-605
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17689081.html
>T: Technique/Tips 分享一个小技术

>S: Share 分享一个观点

https://www.cnblogs.com/505donkey/p/17694610.html

# ARTS打卡第3周
>A: Algorithm 一道算法题

leetcode-1431
>R: Review 读一篇英文文章

https://www.cnblogs.com/505donkey/p/17676185.html
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/articles/17675198.html
>S: Share 分享一个观点

笨功夫是普通人最后的依靠。

# ARTS打卡 第2周
> A: Algorithm 一道算法题

leetcode-1071
>R: Review 读一篇英文文章

https://www.xiaohongshu.com/explore/64e5370d000000000800dbe8?app_platform=ios&app_version=8.2&apptime=1692745577&appuid=6071a92d0000000001003cee&author_share=1
>T: Technique/Tips 分享一个小技术

https://www.cnblogs.com/505donkey/articles/17692317.html
>S: Share 分享一个观点

近日看到日本核污染水排海的新闻，只能说很无耻，对于本来就不好的大环境，又平添多种不确定因素。今年一直说到的AI，都在说要拥抱AI，但实际上这个能多大程度为我们生活品质带来提升，或者未来发展更加笃定，很多人心里其实完全没底，一如很多AI还有多少不为人知的，处于黑盒的一面，但因为对手都在搞，所以我们不搞不行，就算心里感觉不踏实也要搞，但未来会发展成什么样，可能会对我们未来人类造成什么样的影响，不得而知，好比这个福岛核污染水的排放，后者更是赤裸裸地对人类未来公共生存环境资源的透支，这个时候就不禁想，我们技术还可以做些什么，在一系列的不确定中找到让人内心安稳，确定的因素，与这个混沌的熵增世界对抗？还是只能默默接受？

# ARTS-打卡第1周
> A: Algorithm 一道算法题

leetcode-1768
> R: Review 读一篇英文文章

https://www.xiaohongshu.com/explore/64dbf928000000000c0376da?amp=&am=
>T: Technique/Tips 分享一个小技术

RocksDB简介
什么是RocksDB
RocksDB是一个高性能发挥存储硬件性能的嵌入式KV存储引擎，即在通过嵌入式提供卓越存储性能基础之上，给用户进一步提供设计自主数据库的能力，如果网络服务等，它的高性能，主要体现在它对如内存、闪存、存储都有优化，可以通过配置形式进行优化，根据工作中实际应用场景进行配置。
RocksDB历史
RocksDB是基于LevelDB的一个上层封装，或者是基于LevelDB的一个迁移版本，主要维护公司是facebook，其中，LevelDB是由谷歌发明的，其作者就是分布式领域三驾马车（GFS，Gig Table，MapReduce，这三篇论文直接开启了分布式的时代）中Big Table的提出者，目前很多人所了解的比如redis，mysql，mongo等都可以用rocksdb来替换，比如redis的rocksdb版本是pika，mysql的替换版本是myrocks等，rocksdb也是很多分布式存储数据库持久化的参考方案，如TiDB。
RocksDB解决了什么问题
主要解决了一个写多读少的场景需求，比如大数据库的存储、处理等，这点和mysql的innodb（mysql默认数据引擎，使用B+树进行时数据处理）写少读多的特性相反，即RocksDB的读性能是稍逊色于写性能。
> S: Share 分享一个观点

面对新技术的发展，人们往往容易高估技术短期的作用，但又低估技术长期的影响。