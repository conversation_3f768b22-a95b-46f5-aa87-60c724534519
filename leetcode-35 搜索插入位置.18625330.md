给定一个排序数组和一个目标值，在数组中找到目标值，并返回其索引。如果目标值不存在于数组中，返回它将会被按顺序插入的位置。

请必须使用时间复杂度为 O(log n) 的算法。

示例 1:
> 输入: nums = [1,3,5,6], target = 5
输出: 2

示例 2:
> 输入: nums = [1,3,5,6], target = 2
输出: 1

示例 3:
> 输入: nums = [1,3,5,6], target = 7
输出: 4

提示:
- 1 <= nums.length <= 104
- -104 <= nums[i] <= 104
- nums 为 无重复元素 的 升序 排列数组
- -104 <= target <= 104

go实现：
```go
func searchInsert(nums []int, target int) int {

	// 获取给定数组长度
	n := len(nums)

	// 因为题目要求时间复杂度为O(logn)，所以这里采用二分法进行查找，即每次取中间值进行比较
	// 定义左右边界索引，初始值分别为数组第一个和末尾元素对应索引
	left, right := 0, n-1

	// 确定搜索范围为左右边界之间，即当左边界不大于右边界时
	for left <= right {

		// 获取索引区间范围中间值
		mid := left + (right-left)/2

		// 如果中间值与target相同，直接返回mid
		if nums[mid] == target {
			return mid
		} else if nums[mid] > target {
			// 如果中间值大于target，说明目标索引可能在中间值左边范围内，故左区间则为下次搜索范围，所以搜索范围左边界不动，右边界则更新为mid-1（前边计算表明mid不是目标值，所以更新边界时则不包含mid）
			right = mid - 1
		} else {
			// 如果中间值小于target，说明目标值可能在中间值右边，故右区间则为下次搜索范围，同理右边界不动，左边界更新为mid+1
			left = mid + 1
		}
	}

	// 当左边界大于右边界时，遍历结束，返回值取更新后的左边界（因为相等时左边界等于右边界，不等时左边界大于右边界，表示新插入位置，故取左边界值）
	return left
}
```