After the Nobel Prize in physics went to <PERSON> and <PERSON> "for foundational discoveries and inventions that enable machine learning with artificial neural networks", many asked why a prize for physics has gone to computer scientists for what is also an achievement in computer science.
在诺贝尔物理学奖被授予 <PERSON> 和 Geoffrey <PERSON> “褒奖其使能通过人工神经网络进行机器学习的基础发现和发明” 之后，很多人好奇为什么诺贝尔物理学奖会颁给计算机科学家，因为这也是计算机领域的一项重要成就。

Even <PERSON>, a winner of the 2018 Turing Award and one of the "godfathers of AI", was himself "extremely surprised" at receiving the call telling him he had got the Nobel in physics, while the other recipient <PERSON><PERSON> said "It was just astounding."
即便是2018年图灵奖获得者，人工智能教父之一的Hinton，在接到通知他获得诺贝尔物理学奖的电话时感到 “非常吃惊”，而另一位获奖者Hopfield则表示 “这太令人震惊了”。

Actually, the artificial neural network research has a lot to do with physics. Most notably, <PERSON><PERSON> replicated the functioning of the human brain by using the self-rotation of single molecules as if they were neurons and linking them together into a network, which is what the famous Hopfield neural network is about. In the process, <PERSON><PERSON> used two physical equations. Similarly, <PERSON><PERSON> made <PERSON><PERSON>'s approach the basis for a more sophisticated artificial neural network called the Boltzmann machine, which can catch and correct computational errors.
实际上，人工神经网络研究和物理学有密切的关系。值得注意的是，Hopfield 通过使用单分子自转来复制人类大脑的功能，就把他们当成是神经元一样，把它们连接到一起组成一个网络，这就是著名的 Hopfield 神经网络意义所在。在这个过程中，Hopfield 使用了两个物理方程式。同样的，Hinton 按照 Hopfield 的方法，把它作为一种名为 Boltzmann 机器的更加复杂的人工神经网络的基础，该网络可以捕捉并修正计算错误。

The two steps have helped in forming a net that can act like a human brain and compute. The neural networks today can learn from their own mistakes and constantly improve, thus being able to solve complicated problems for humanity. For example, the Large Language Model that's the basis of the various GPT technologies people use today dates back to the early days when Hopfield and Hinton formed and improved their network.
这两个步骤可以帮助形成一个可以像人脑一样工作和计算的网络。该神经网络可以从他们的错误中学习并持续改进，因此可以帮助人类解决复杂问题。举个例子，今天人们在使用的各种基于大语言模型的GPT技术，可以追溯到 Hopfield 和 Hinton 在形成和改进他们的网络的早期。

Instead of weakening the role of physics, that the Nobel Prize in Physics goes to neural network achievements strengthens it by revealing to the world the role physics, or fundamental science as a whole, plays in sharpening technology. Physics studies the rules followed by particles and the universe and paves the way for modern technologies. That is why there is much to thank physicists for the milestones modern computer science has crossed.
诺贝尔物理学奖被授予神经网络的成就，不仅没有削弱物理学的作用，反而通过向世界揭示物理学的作用，或者作为整个基础科学在提升技术上等来强化它。物理学研究粒子和宇宙遵循的规则，为现在技术的发展铺平的道路。这也是为什么现代计算机科学可以跨越里程碑要感谢物理学家的原因。