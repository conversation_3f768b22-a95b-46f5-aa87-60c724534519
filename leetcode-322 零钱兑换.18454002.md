给你一个整数数组 coins ，表示不同面额的硬币；以及一个整数 amount ，表示总金额。
计算并返回可以凑成总金额所需的 最少的硬币个数 。如果没有任何一种硬币组合能组成总金额，返回 -1 。
你可以认为每种硬币的数量是无限的。

示例 1：
输入：coins = [1, 2, 5], amount = 11
输出：3 
解释：11 = 5 + 5 + 1

示例 2：
输入：coins = [2], amount = 3
输出：-1

示例 3：
输入：coins = [1], amount = 0
输出：0

提示：
1 <= coins.length <= 12
1 <= coins[i] <= 231 - 1
0 <= amount <= 104

实现：
```go
func coinChange(coins []int, amount int) int {

    // 定义动态数据dp，用于保存不同金额对应的最小硬币数，长度为amount+1，因为考虑amount为0的情况
    dp := make([]int, amount + 1)

    // 定义dp初始状态，如果金额为0时硬币数也为0，其他金额则先暂定一个大数，这里取比amount大1的数值
    dp[0] = 0
    for i:=1; i<=amount; i++ {
        dp[i] = amount + 1
    }

    // 从金额1开始检查，并且每个金额都会重新检查硬币组合，如果当前硬币不大于当前金额，则当前金额减去当前硬币面值后的个数+1，和当前金额个数的中较小值作为当前金额个数
    for amout:=1; amout<=amount; amout++ {
        for _, coin := range coins {
            if coin <= amout {
                dp[amout] = min(dp[amout - coin] + 1, dp[amout])
            }
        }
    }

    // 检查指定金额个数是否为之前指定的默认值，如是则表示未匹配到最小硬币数，返回-1
    if dp[amount] == amount+1 {
        return -1
    }

    // 返回指定金额对应硬币个数
    return dp[amount]
}

// 辅助函数，获取较小值
func min(a,b int) int {
    if a <= b {
        return a
    }
    return b
}
```