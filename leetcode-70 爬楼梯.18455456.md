假设你正在爬楼梯。需要 n 阶你才能到达楼顶。
每次你可以爬 1 或 2 个台阶。你有多少种不同的方法可以爬到楼顶呢？

示例 1：
输入：n = 2
输出：2
解释：有两种方法可以爬到楼顶。
1. 1 阶 + 1 阶
2. 2 阶

示例 2：
输入：n = 3
输出：3
解释：有三种方法可以爬到楼顶。
1. 1 阶 + 1 阶 + 1 阶
2. 1 阶 + 2 阶
3. 2 阶 + 1 阶

提示：
1 <= n <= 45

实现：
```go
func climbStairs(n int) int {

    // 如果爬楼阶数不大于2，则直接返回n
    if n <= 2 {
        return n
    }

    // 定义dp储存不同阶数爬楼方法
    dp := make([]int, n+1)
    dp[1] = 1
    dp[2] = 2

    // 从3阶开始累计每个阶数的方法
    for i:=3; i<=n; i++ {
        dp[i] = dp[i-2] + dp[i-1]
    }

    return dp[n]

}
```