# 组件基础样式控制

## 行内样式（不推荐）

### 方式1
- 
    ```js
    function App() {
        return (
            <div>
                <span style={{color: 'red', fontSize: '50px'}}>this is span</span>
            </div>
        );
    }
    ...
    ```
### 方式2
- 
    ```js
    const style = {
        color: 'red',
        fontSize: '50px'
    }

    function App() {
        return (
            <div>
                <span style={style}>this is span</span>
            </div>
        );
    }
    ...
    ```

## class类名控制
- 
    ```js
    import './index.css'

    function App() {
        return (
            <div>
                <span className="foo">this is span</span>
            </div>
        );
    }
    ...
    ```