给定两个字符串 s 和 t ，判断它们是否是同构的。
如果 s 中的字符可以按某种映射关系替换得到 t ，那么这两个字符串是同构的。
每个出现的字符都应当映射到另一个字符，同时不改变字符的顺序。不同字符不能映射到同一个字符上，相同字符只能映射到同一个字符上，字符可以映射到自己本身。

示例 1:
输入：s = "egg", t = "add"
输出：true

示例 2：
输入：s = "foo", t = "bar"
输出：false

示例 3：
输入：s = "paper", t = "title"
输出：true

提示：
1 <= s.length <= 5 * 104
t.length == s.length
s 和 t 由任意有效的 ASCII 字符组成

实现：
```go
func isIsomorphic(s string, t string) bool {

    // 定义s、t的结构映射，kv格式均为rune
    maps_s_to_t := make(map[rune] rune)
    maps_t_to_s := make(map[rune] rune)

    // 遍历字符串，因为是同构，所以可以共用索引
    for i, sc := range s {

        tc := rune(t[i])

        // 如果字符在映射中存在，则检查映射值是否相等，不相等则返回false，全部都遍历完则返回true
        if mc, ok := maps_s_to_t[sc]; ok {
            if mc != tc {
                return false
            }
        } else {
            maps_s_to_t[sc] = tc
        }

        if mc, ok := maps_t_to_s[tc]; ok {
            if mc != sc {
                return false
            } 
        } else {
            maps_t_to_s[tc] = sc
        }
    }

    return true
}
```