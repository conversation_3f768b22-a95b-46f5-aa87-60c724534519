# 分类和商品列表渲染

## 实现步骤
- 启动项目（mock服务+前端服务）
- 使用RTK编写store（异步action）
- 组件触发action并且渲染数据

## 实现示例

### takeaway.js
- 
```js
// 编写store
import { createSlice } from '@reduxjs/toolkit'
import axios from 'axios'

const foodsStore = createSlice({
    name: 'foods',
    initialState: {
        // 商品列表
        foodsList: []
    },
    reducers: {
        setFoodsList(state, action) {
            state.foodsList = action.payload
        }
    }
})

// 异步获取部分
const { setFoodsList } = foodsStore.actions // 解构foodsStore方法

const fetchFoodsList = () => {
    return async (dispatch) => {
        // 编写异步逻辑
        const res = await axios.get('http://localhost:3004/takeaway')
        // 调用dispatch函数提交action
        dispatch(setFoodsList(res.data))
    }
}

export { fetchFoodsList }

const reducer = foodsStore.reducer

export default reducer
```

### store/index.js
- 
```js
import foodsReducer from './modules/takeaway'
import { configureStore } from "@reduxjs/toolkit";

const store = configureStore({
    reducer: {
        foods: foodsReducer
    }
})

export default store
```

### App.js
- 
```js
import NavBar from './components/NavBar'
import Menu from './components/Menu'
import Cart from './components/Cart'
import FoodsCategory from './components/FoodsCategory'

import './App.scss'
import { useDispatch, useSelector } from "react-redux";
import { fetchFoodsList } from "./store/modules/takeaway";
import { useEffect } from 'react'

const App = () => {

  // 触发action执行
  // 1. useDispatch -> dispatch 2. actionCreater 导入进来 3. useEffect
  const dispatch = useDispatch()
  useEffect(() => {
    dispatch(fetchFoodsList())
  }, [dispatch])

  // 获取foodsList渲染数据列表
  // 1. useSelector
  const { foodsList } = useSelector(state => state.foods)

  return (
    <div className="home">
      {/* 导航 */}
      <NavBar />

      {/* 内容 */}
      <div className="content-wrap">
        <div className="content">
          <Menu />

          <div className="list-content">
            <div className="goods-list">
              {/* 外卖商品列表 */}
              {foodsList.map(item => {
                return (
                  <FoodsCategory
                    key={item.tag}
                    // 列表标题
                    name={item.name}
                    // 列表商品
                    foods={item.foods}
                  />
                )
              })}
            </div>
          </div>
        </div>
      </div>

      {/* 购物车 */}
      <Cart />
    </div>
  )
}

export default App
```

### index.js
- 
```js
import React from 'react'
import { createRoot } from 'react-dom/client'

import App from './App'

// 注入store
import { Provider } from "react-redux";
import store from "./store";

const root = createRoot(document.getElementById('root'))
root.render(
  <Provider store={store}>
    <App />
  </Provider>

)
```