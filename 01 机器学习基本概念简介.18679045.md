# 定义
- 定义：机器学习，就是让机器具备找一个函式的能力（look for function），且涵盖多种任务。
- 应用举例
    - speech recognition 语音识别
    - image recognition 图像识别
    - playing go 围棋

# 任务类别
- regression（回归）：函数会输出一个标量（Scala）。
    - 应用：PM2.5值预测（输入今天PM2.5值、温度、臭氧含量等，然后函数输出第二天的PM2.5预测值）
- classification（分类）：给定选项（或类别）后，函数会输出正确的选项。
    - 应用1：垃圾邮件过滤（spam filtering）
    - 应用2：alpha go
- structure learning（结构化学习）：输出具有结构性的东西（图像，文档），即让机器学会创造。

# 机器学习步骤
1. function with unknown parameters
    - 说明：带未知参数函数，即model。
    - model 基础公式：$y = b + w*x_1$
    - $x_1$：代表已知参数部分，即 feature。
    - w：表示未知参数权重部分，即 weight，直接与feature相乘。
    - b：表示未知参数偏差部分，即 bias，是直接加上去的。
2. define loss from training data
    - 说明：从训练数据中定义损失，loss 也是一个函数，输入是未知参数b和w，输出对未知参数值合适程度判断，即预测值与实际值的误差均值。
    - 误差计算方式
        - MAE：mean absolute error，即预测值与实际值差距绝对值。
        - MSE：mean square error，即预测值与实际值差距平。
        - Cross-entropy：预测值与实际值为几率分布（probability distributions）
    - error surface：修正参数b和权重参数w等高线图
3. optimization
    - 说明：最佳化，即找出使损失最小的w和b值。
    - 基础公式：$w^*,b^*=arg{min \choose w,b}L$
    - 方法
        - gradient descent 梯度下降
            1. ramdomly pick an initial value $w^0$ 
                随机选取一个初始值 $w^0$；
            2. coupute $\frac{\partial L}{\partial w}|w=w^0$ 计算在权重等于 $w^0$ 时的微分（$w^0$ 区间范围单点斜切率）；
                - hyperparamters 机器学习过程中需要自己设定的参数。
            3. update w iteratively 通过持续更新学习率，来控制每次迭代中模型参数w更新的步长。
                - 学习率基本公式：$w^1 \leftarrow w^0 - η \frac{\partial L}{\partial w}|w=w^0$
            - loss 计算常见问题，得出 local minima （局部最小值）就停止，而不是直至获得 global minima（全局最小值）。
        - 单个到多个参数的计算方式均可参考以上3个步骤。
- 这类模型称为 linear models，此类模型有个比较大的问题，就是限制比较多，无法有效模拟真实的状况。