给你一个非负整数 x ，计算并返回 x 的 算术平方根 。
由于返回类型是整数，结果只保留 整数部分 ，小数部分将被 舍去 。
注意：不允许使用任何内置指数函数和算符，例如 pow(x, 0.5) 或者 x ** 0.5 。

示例 1：
输入：x = 4
输出：2

示例 2：
输入：x = 8
输出：2
解释：8 的算术平方根是 2.82842..., 由于返回类型是整数，小数部分将被舍去。

提示：
0 <= x <= 2^31 - 1

给出思路，但不要代码

实现：
```go
func mySqrt(x int) int {

	// 如果x为0或1，平方根即是他们本身，故直接返回
	if x < 2 {
		return x
	}

	// 定义平方根范围（左右边界），此处使用二分法求解
	left, right := 0, x

	// 当左边界不大于右边界时，调整平方根范围
	for left <= right {

		// 计算左右边界中位数
		mid := (left + right) / 2

		// 如果mid平方大于x，说明平方根在区间右半部分，需要更新右边界，又因为mid此时大于x平方根且取值只取整数部分，故调整右边界值为mid-1
		if mid > x/mid {
			right = mid - 1
			// 如果mid平方小于x，说明平方根在区间左半部分，则调整左边界为mid+1
		} else {
			left = mid + 1
		}
	}

	// 当左边界大于右边界时，退出循环，此时右边界指向的是最后一个满足mid平方根小于x的值，而左边界的指向是第一个平方大于x的平方根的值，故取右边界值
	return right
}
```