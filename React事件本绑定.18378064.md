# 事件绑定

## 基础事件绑定
- 语法：on + 事件名称 = {事件处理程序}，整体上遵循驼峰命名法
- 
    ```js
    function App() {
        const handleClick = () => {
            console.log('button被点击了')
        }
        return (
            <div className="App">
            <button onClick={handleClick}>click me</button>
            </div>
        );
    }
    ```

## 使用事件对象参数
- 语法：在事件回调函数中设置形参e
- 
    ```js
    function App() {
        const handleClick = (e) => {
            console.log('button被点击了', e)
        }
        return (
            <div className="App">
            <button onClick={handleClick}>click me</button>
            </div>
        );
    }
    ```

## 传递自定义参数
- 语法：事件绑定的位置改造成箭头函数的写法，在执行 clickhandle 实际处理业务函数的时候传递实参。
- 
    ```js
    function App() {
        const handleClick = (name) => {
            console.log('button被点击了', name)
        }
        return (
            <div className="App">
            <button onClick={() => handleClick('dky')}>click me</button>
            </div>
        );
    }
    ```

## 同时传递事件对象和自定义参数
- 语法：在事件绑定的位置传递事件实参e和自定义参数，clickhandle 中声明形参，注意顺序对应。
- 
    ```js
    function App() {
        const handleClick = (name, e) => {
            console.log('button被点击了', name, e)
        }
        return (
            <div className="App">
            <button onClick={(e) => handleClick('dky', e)}>click me</button>
            </div>
        );
    }
    ```