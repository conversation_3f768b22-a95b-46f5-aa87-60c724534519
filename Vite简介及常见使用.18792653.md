## 概念简介

#### 定义解析
Vite（法语"快速"）是由 Vue.js 创始人尤雨溪开发的**前端构建工具**，其技术特性有：
- 基于 ESM（ECMAScript Modules）的现代模块系统；
- 开发环境与生产环境分离的双引擎架构
- 面向浏览器原生特性的编译优化工具链

也是目前 React 官方主推的脚手架工具。

#### 优势对比

| 维度     | Webpack           | Vite               | 技术差异解析                    |
| -------- | ----------------- | ------------------ | ------------------------------- |
| 编译策略 | 全量打包          | 按需编译           | 利用浏览器原生 ESM 实现动态加载 |
| 启动机制 | 构建依赖图谱      | 直接启动服务       | 跳过 AST 解析和打包步骤         |
| HMR 效率 | 局部更新+全量重载 | 精准模块热替换     | 基于 ESM 的模块边界追踪         |
| 构建工具 | 自研打包器        | Rollup（生产环境） | 生产构建采用更高效的打包方案    |

> **术语解释**  
> ESM：ECMAScript Modules，浏览器原生支持的模块系统，通过 `import/export` 语法实现模块化  
> HMR：Hot Module Replacement，模块热替换技术，无需刷新页面更新代             

## 核心原理

#### 工作流程
```mermaid
sequenceDiagram
    Browser->>Vite Server: 发起 ESM 请求
    Vite Server->>Compiler: 拦截请求分析依赖
    Compiler->>Vite Server: 实时编译(SASS/JSX等)
    Vite Server->>Browser: 返回编译后ESM
    Browser->>Vite Server: 发起HMR更新请求
    Vite Server->>Compiler: 差异编译变更模块
    Compiler->>Browser: 精准推送更新模块
```

#### 环境差异处理

| 特性         | 开发环境                          | 生产环境                         |
| ------------ | --------------------------------- | -------------------------------- |
| **核心工具** | esbuild（预构建） + Vite 实时编译 | Rollup（完整打包）               |
| **代码处理** | 按需转译，保留源码结构            | 代码压缩/混淆，Tree Shaking 优化 |
| **模块加载** | 浏览器直接请求源码                | 生成静态资源文件                 |
| **性能优化** | 热更新（HMR）                     | Code Splitting，预加载指令       |

> **术语解释**
> - esbuild：js打包工具（Go编写），侧重即时反馈和调试便利性，主要用于开发环境进行快速预构建依赖（把node_modules转换成浏览器可识别的ES模块）。
> - Rollup：js模块打包，侧重加载性能和资源优化（生成最小化静态文件），主要用于生产环境进行完整打包，如支持 Tree Shaking（自动移除未使用的代码）、Code Splitting（将大代码库拆分成多个小块） 等功能。

## 适用场景

1. **新项目开发**：适合中大型单页应用(SPA)和服务端渲染(SSR)项目，主要用于复杂交互型应用，如管理后台、社交平台等（利用原生ESM实现闪电般快的启动和热更新）。
2. **旧项目迁移**：可逐步替代Webpack等传统打包器，提升开发效率（已有成熟迁移工具链）。
3. **微前端架构**：天然支持基于ES模块的动态导入，实现真正的运行时模块按需加载。
4. **快速原型验证**：通过官方提供的模板库（React/Vue/Svelte等），一条命令即刻搭建完整开发环境（ 如 `npm create vite`）。

**【限制】**
- 兼容性：对于旧版浏览器（如 IE11）需通过 @vitejs/plugin-legacy 添加 polyfill 才能支持。
- 复杂场景：对于超大型项目可能需要定制 Rollup 配置（通过 vite.config.js 实现）。
- 插件生态：部分 Webpack 插件（如 DLLPlugin）无 Vite 替代方案，推荐优先使用 Vite 官方插件，并通过 vite-plugin-xxx 兼容层进行转换。

## 快速上手

#### 创建项目

以 React 为例

```bash
# 使用 npm 创建项目（选择react模板）
npm create vite@latest my-react-app -- --template react

# 进入项目目录
cd my-react-app

# 安装依赖
npm install

# 启动开发服务器（开发模式）
npm run dev
```

#### 项目结构

**初始结构**
```
my-react-app/
├── README.md                # 项目说明文档（含启动命令、部署指南等）
├── eslint.config.js         # ESLint 静态检查配置（替代旧版 .eslintrc）
├── index.html               # 应用入口 HTML（Vite 从这里加载 JS，支持直接使用 ESM 模块）
├── node_modules/            # 依赖库（自动生成，勿手动修改）
├── package-lock.json        # 精确依赖版本锁定（自动生成）
├── package.json             # 项目配置和依赖声明
├── public/                  # 静态资源目录（直接复制到构建输出），存放无需处理的静态文件（如 favicon.ico、robots.txt）
├── src/                     # 核心开发目录
│   ├── App.jsx              # 根组件，组件文件使用 .jsx 扩展名
│   ├── App.css              # 样式文件与组件同名（如 App.css 与 App.jsx）    
│   ├── main.jsx             # JS 入口文件，用于初始化 React 根节点，挂载主组件 <App />
│   └── ... 
└── vite.config.js           # Vite 构建配置文件（构建、代理等）
```

**最佳实践**
```bash
# 组件目录优化
src/
├── components/
│   ├── base/            # 基础组件库（工程化封装）
│   │   ├── Button/      # 包含完整测试用例
│   │   │   ├── index.jsx
│   │   │   ├── Button.stories.jsx  # 可视化文档
│   │   │   └── test/
│   ├── features/        # 业务组件库（按模块划分）
│   │   └── payment/     # 支付业务域
│   │       └── PayButton.jsx  # 继承自base/Button
├── assets/
│   ├── images/          # 图片资源（自动处理格式转换）
│   ├── fonts/           # 字体文件（WOFF2优先）
│   └── styles/          # 全局样式（主题变量/动画定义）
├── layouts/             # 页面布局组件（导航/页脚等全局结构）
├── hooks/               # 自定义Hook集合（useFetch等可复用逻辑）
├── libs/                # 通用工具函数
└── config/              # 组件库全局配置
```

#### 常用命令

**开发模式**
```bash
# 启动开发模式
npm run dev    

# 指定端口启动
npm run dev --port 3000

# 自动打开浏览器
npm run dev --open
```

**构建打包**
```bash
# 构建生产包（生成上线文件，默认输出到/dist）
npm run build  

# 指定环境模式构建
npm run build --mode staging

# 构建后监听文件变化
npm run build --watch
```

**预览模式**
```bash
# 本地预览生产包
npm run preview

# 指定预览端口
npm run preview --port 5000 
```

**其他命令**
```bash
# 可视化分析包体积（需安装rollup-plugin-visualizer）
npx vite analyze  

# 手动执行依赖预优化
npx vite optimize 
```

#### 常用插件

**核心插件**
- **@vitejs/plugin-react**：React 项目必备，支持 JSX/TSX 转换、React Fast Refresh（热更新不丢失状态）等。
- **@vitejs/plugin-vue**：支持 Vue 3 SFC 单文件组件解析，内置模板编译优化，比 vue-loader 快 2-3 倍。

**静态资源处理**
- **vite-plugin-svgr**：将 SVG 转换为 React 组件（类似 Webpack 的 svgr-loader），支持 SVG 属性动态修改。
- **vite-plugin-static-copy**：将静态文件（如 PDF/字体）直接复制到构建目录，适合非模块化资源处理。

**工程化增强**
- **vite-plugin-pwa**：自动生成 Service Worker 和 manifest.json，添加 PWA（渐进式 Web 应用）支持。
- **vite-plugin-mock**：快速创建本地 mock 数据（支持 REST/GraphQL），可对接 Swagger 文档自动生成 mock。

**性能优化**
- **vite-plugin-compression**：自动生成 gzip/brotli 压缩文件。
- **vite-plugin-cdn-import**：将依赖替换为 CDN 链接（减少打包体积），适合 React/Vue 等大型框架的 CDN 加速。

**调试辅助**
- **vite-plugin-inspect**：可视化检查 Vite 中间状态，即转换后的代码/依赖图（如访问 http://localhost:5173/__inspect/）。
- **vite-plugin-checker**：实时进行 TypeScript/ESLint 校验，且不阻塞开发服务器启动。