# useState

## 介绍
- useState 是一个 React hook（函数），它运行我们向组件添加一个状态变量，从而控制影响组件的渲染结果。

## 本质
- 和普通JS变量不同的是，状态变量一旦发生变化，组件的视图UI也会跟着变化（数据驱动视图）。

## 特点
- useState 是一个函数，返回值是一个数组
- 数组中的第一个参数是状态变量，第二个参数是set函数用来修改状态变量
- useState 参数将作为状态变量参数的初始值

## 基础使用
- 示例：实现一个计数器按钮
- 
    ```js
    import { useState } from 'react'

    function App() {

        // 1. 调用 useState 添加一个状态变量
        // count 状态变量，setCount 修改状态变量的方法
        const [count, setCount] = useState(0);

        // 2. 点击事件回调
        const handClick = () => {
            // 用传入的新值修改count，count应用新值后会重新渲染UI
            setCount(count + 1); 
        }

        return (
            <div className="App">
                <button onClick={handClick}>{count}</button>
            </div>
        );
    }
    ...
    ```

## 修改状态
- 状态不可变
    - 在react中，状态被认为是只读的，我们应该始终替换它而不是修改它，直接修改状态不能引发视图更新。
- 修改对象状态
    - 规则：对于对象类型的状态变量，应该始终传给set方法一个全新的的对象来进行修改。
    - 
        ```js
        import { useState } from 'react'

        function App() {

            const [form, setForm] = useState({name: 'jack'});

            const changeForm = () => {
                setForm({
                ...form,
                name: 'dky'
                })
            }

            return (
                <div className="App">
                    <button onClick={changeForm}>修改form：{form.name}</button>
                </div>
            );
        }
        ...
        ```