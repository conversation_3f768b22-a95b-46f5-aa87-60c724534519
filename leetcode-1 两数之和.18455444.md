给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出 和为目标值 target  的那 两个 整数，并返回它们的数组下标。
你可以假设每种输入只会对应一个答案，并且你不能使用两次相同的元素。
你可以按任意顺序返回答案。

示例 1：
输入：nums = [2,7,11,15], target = 9
输出：[0,1]
解释：因为 nums[0] + nums[1] == 9 ，返回 [0, 1] 。

示例 2：
输入：nums = [3,2,4], target = 6
输出：[1,2]

示例 3：
输入：nums = [3,3], target = 6
输出：[0,1]

提示：
2 <= nums.length <= 104
-109 <= nums[i] <= 109
-109 <= target <= 109
只会存在一个有效答案

实现：
```go
func twoSum(nums []int, target int) []int {

	// 初始化数字与索引映射存储容器
    num_indexes := make(map[int]int)

	// 遍历nums，获取target和当前元素的差值diff
    for i, num := range nums {
		diff := target - num
        // 如果差值diff在容器num_indexes中存在，则返回当前元素和diff索引，结束遍历
		if _, ok := num_indexes[diff]; ok {
			return []int{i, num_indexes[diff]}
		}
        // 如果差值diff在容器num_indexes中不存在，则将当前元素作为新键加入num_indexes
        num_indexes[num] = i
	}

	// 如果遍历结束未发现正确答案，则返回空数组
    return []int{}
}
```