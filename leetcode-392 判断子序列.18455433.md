给定字符串 s 和 t ，判断 s 是否为 t 的子序列。
字符串的一个子序列是原始字符串删除一些（也可以不删除）字符而不改变剩余字符相对位置形成的新字符串。（例如，"ace"是"abcde"的一个子序列，而"aec"不是）。

进阶：
如果有大量输入的 S，称作 S1, S2, ... , Sk 其中 k >= 10亿，你需要依次检查它们是否为 T 的子序列。在这种情况下，你会怎样改变代码？
 
示例 1：
输入：s = "abc", t = "ahbgdc"
输出：true

示例 2：
输入：s = "axc", t = "ahbgdc"
输出：false

提示：
0 <= s.length <= 100
0 <= t.length <= 10^4
两个字符串都只由小写字符组成。

实现：
```go
func isSubsequence(s string, t string) bool {

    // 因为空字符串可以为任何字符串子串，所以返回true
    if len(s) == 0 {
        return true
    }

    // 定义字符串s索引si
    si := 0

    // 遍历字符串t，如果当前字符和s相同，则移动si，否则继续遍历
    // 因为要遍历到最后一个字符，所以ti最后一次自增后相当于len(t)，s同理
    for ti:=0; ti<len(t); ti++ {

        if t[ti] == s[si] {
            si++
        }

        // 如果si已经达到字符串s长度，则返回true
        if si == len(s) {
            return true
        }
        
    }

    return false
}
```