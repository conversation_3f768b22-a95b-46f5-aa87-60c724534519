给定一个正整数 n，编写一个函数，获取一个正整数的二进制形式并返回其二进制表达式中 
设置位的个数（也被称为汉明重量）。

示例 1：
> 输入：n = 11
输出：3
解释：输入的二进制串 1011 中，共有 3 个设置位。

示例 2：
> 输入：n = 128
输出：1
解释：输入的二进制串 10000000 中，共有 1 个设置位。

示例 3：
> 输入：n = 2147483645
输出：30
解释：输入的二进制串 1111111111111111111111111111101 中，共有 30 个设置位。

提示：
- 1 <= n <= 231 - 1

go实现1：
```go
func hammingWeight(n int) int {

	// 使用标准库方法，直接将给定整数n，转为二进制表示的字符串，赋值给变量bin
	bin := fmt.Sprintf("%b", n)

	// 定义位1计数器，初始值为0
	count := 0

	// 对转为结果进行遍历，如果当前位为字符1（因为bin为字符串），则计数器加1
	for i := 0; i < len(bin); i++ {
		if bin[i] == '1' {
			count++
		}
	}

	// 返回计数器结果
	return count

}
```

go实现2：
```go
func hammingWeight(n int) int {

	// 初始化计数器，用于计算位1个数
	count := 0

	// 当给定n不为0时，对n进行位与运算
	for n != 0 {
		// 与运算从n末位开始（即最右边），然后所有位数往右进1位，并左补零
		count += n & 1
		n >>= 1
	}

	// 返回计数结果
	return count
}
```