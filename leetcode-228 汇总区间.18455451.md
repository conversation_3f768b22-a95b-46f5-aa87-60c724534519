给定一个  无重复元素 的 有序 整数数组 nums 。
返回 恰好覆盖数组中所有数字 的 最小有序 区间范围列表 。也就是说，nums 的每个元素都恰好被某个区间范围所覆盖，并且不存在属于某个范围但不属于 nums 的数字 x 。

列表中的每个区间范围 [a,b] 应该按如下格式输出：
"a->b" ，如果 a != b
"a" ，如果 a == b

示例 1：
输入：nums = [0,1,2,4,5,7]
输出：["0->2","4->5","7"]
解释：区间范围是：
[0,2] --> "0->2"
[4,5] --> "4->5"
[7,7] --> "7"

示例 2：
输入：nums = [0,2,3,4,6,8,9]
输出：["0","2->4","6","8->9"]
解释：区间范围是：
[0,0] --> "0"
[2,4] --> "2->4"
[6,6] --> "6"
[8,9] --> "8->9"

提示：
0 <= nums.length <= 20
-231 <= nums[i] <= 231 - 1
nums 中的所有值都 互不相同
nums 按升序排列

实现：
```go
import "strconv"
import "fmt"

func summaryRanges(nums []int) []string {

    // 定义返回对象
    var result []string

    // 如果nums为空，则直接返回空结果
    if len(nums) == 0 {
        return result
    }

    // 定义区间过程变量，start为区间开始，初始值为nums第一个元素
    // prev为遍历过程中当前元素的上一个元素
    start := nums[0]
    prev := start

    // 如果存在第二个元素，则从该元素开始遍历
    for i:=1; i<len(nums); i++ {

        // 如果当前元素和上一个元素差值不为1，则进行区间判断
        if nums[i] != prev + 1 {
            
            // 如果上一个元素与区间开始值start相同，区间则为start，反之则为start到prev
            if start == prev {
                result = append(result, strconv.Itoa(start))
            } else {
                result = append(result, fmt.Sprintf("%d->%d", start, prev))
            }

            // 新增区间后，将过程变量start重置为当前元素
            start = nums[i]
        }

        // 判断借宿后，prev重置为当前元素
        prev = nums[i]
    }

    // 不满足遍历条件或遍历结束后，将最后的区间加入到result中
    if start == prev {
        result = append(result, strconv.Itoa(start))
    } else {
        result = append(result, fmt.Sprintf("%d->%d", start, prev))
    }

    return result
}
```