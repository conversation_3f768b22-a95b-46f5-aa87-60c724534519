# 一 需求梳理

## 1 业务背景

### 1.1 业务挑战

- **测试复杂度高**：需跨硬件（cpu/存储/网卡/内存）、系统（redhat/ubuntu）、软件（数据库/应用）进行多维适配。
  
- **测试强度大**：版本发布周期短（功能+性能），商务出货集中检验（可用+性能），测试需高频执行并反馈结果。
  
- **自动化不足**：主要依赖人工经验（环境准备，用例执行，报告出具），执行效率和质量较低，性能测试具备自动化潜力但未充分释放（功能测试差异大，性能测试指标固定）。
  

## 2 业务需求

> **期望收益**
> 
> - 测试团队：减少重复劳动，标准化测试流程，快速生成报告。
>   
> - 研发团队：快速获取精准性能数据以优化硬件选型及软件适配。
>   
> - 管理层：控制测试成本，缩短合同交付时间。
>   

### 2.1 核心目标

> **优化方向**：将需求归纳为技术设计的关键维度。
> 
> - 抓大放小：围绕成本（人员，精力，时间，预算）设定实施计划（短期，中期，长期）。
>   
> - 适当剪枝：深度锚定业务价值点，重点关注业务价值高关联度需求功能。
>   

- **精准测试能力**：
  
  - **硬件测试**：覆盖CPU（如型号/频率/核心数）、磁盘（如/IOPS）、内存（如容量/速度）、网卡（如带宽/延迟）关键性能指标。
    
  - **软件测试**：覆盖数据库（MySQL/MariaDB）关键性能参数（如TPS/QPS、连接数、查询延迟）。
    
- **用户友好性**：
  
  - **简单部署**：支持单机Docker部署，无复杂依赖。
    
  - **快速操作**：操作流程简化，如 ”主机添加、测试任务配置、报告导出“ 三步完成核心流程。
    
- **结构化报告**：包含硬件配置、系统信息、性能参数、优化建议，支持导出。
  
- **轻量化架构**：尽量使用成熟开源工具，减少开发成本。
  
- **轻量化部署**：容器化封装测试平台核心组件，如 Web前端+任务调度引擎+数据库。
  
- **平衡性能与成本**：如数据采集精度误差<5%，测试任务并发数≥10台。
  

### 2.2 功能需求

| **模块**       | **核心功能**                                                                                          |
| -------------- | ----------------------------------------------------------------------------------------------------- |
| **主机管理**   | 界面化主机添加（如Agent或SSH）、主机分组与标签管理                                                    |
| **数据采集**   | 硬件配置识别（CPU/内存/存储/网络）、系统信息抓取、实时性能指标监控                                    |
| **测试执行**   | 测试任务编排，支持硬件性能（CPU/磁盘/网络/内存）和数据库（MySQL/MariaDB）自动化测试，可自定义测试参数 |
| **报告生成**   | 自动生成结构化报告，包含硬件配置、性能指标、瓶颈分析建议，支持PDF/Excel导出                           |
| **用户与权限** | 测试与研发团队分角色访问（如普通用户、管理员），操作日志审计                                          |

### 2.3 非功能需求

| **维度**       | **具体要求**                                                                          |
| -------------- | ------------------------------------------------------------------------------------- |
| **性能**       | 测试任务并发执行（如同时测试多台主机），单任务执行时间可控（避免资源争抢）            |
| **易用性**     | 界面操作简单直观，5分钟内完成主机添加和测试任务配置，提供操作引导文档                 |
| **可扩展性**   | 支持未来新增测试类型（如其他数据库或中间件）、硬件架构（如ARMv9）和操作系统（如RHEL） |
| **部署简易性** | 支持容器化部署（如Docker Compose），一键脚本初始化依赖环境                            |
| **可靠性**     | 测试任务失败自动重试，测试数据持久化存储，避免数据丢失                                |

# 二 架构设计

> **优化方向**：按照C4模型架构规范进行分层描述。

## 1 上下文图

> **适用场景**：给老板汇报系统整体价值，或向新人解释“我们的系统是做什么的”。

```mermaid
%% C4 Level 1: 系统上下文图
C4Context
  title 性能测试平台

  Person(用户, "测试工程师/研发人员")
  System(测试平台, "性能测试平台")
  System_Ext(被测主机, "物理/虚拟机")
  System_Ext(外部数据库, "被测数据库实例","MySQL/MariaDB")

  Rel(用户, 测试平台, "界面操作")
  Rel(测试平台, 被测主机, "采集数据/执行测试", "SSH/Agent协议")
  Rel(测试平台, 外部数据库, "执行基准测试", "JDBC/TCP")
```

**关键决策说明：**

1. **外部系统界定**
  
  - 被测主机与数据库独立于平台，体现**轻量化部署需求**：平台自身不绑定特定测试目标，通过SSH/Agent实现无侵入接入
  - 外部数据库仅限MySQL/MariaDB，响应**当前软件测试需求**，同时预留JDBC协议为未来扩展做准备
2. **用户交互设计**
  
  - 统一操作入口：通过"界面操作"抽象多种用户角色（测试/研发），满足**分角色访问需求**
  - 协议分层：SSH用于基础测试，JDBC用于数据库测试，符合**精准测试能力**中的硬件/软件分离原则

## 2 容器图

> **适用场景**：技术方案评审，主要给架构师或运维看，讨论技术选型。

```mermaid
%% C4 Level 2: 容器图
C4Container
  title 性能测试平台架构

  Person(用户, "测试工程师/研发人员")
  System_Boundary(平台边界, "性能测试平台") {
    Container(web_ui, "Web前端", "Vue.js", "提供可视化操作界面")
    Container(backend, "后端服务", "Go", "REST API服务")
    Container(task_scheduler, "任务调度引擎", "Celery+Redis", "测试任务编排")
    ContainerDb(platform_db, "平台数据库", "MySQL", "存储测试数据/配置")
  }
  System_Ext(被测主机, "物理/虚拟机")
  System_Ext(外部数据库, "被测数据库实例")

  Rel(用户, web_ui, "操作界面", "HTTPS")
  Rel(web_ui, backend, "API调用", "REST/HTTP")
  Rel(backend, task_scheduler, "提交任务", "REST/HTTP")
  Rel(backend, 被测主机, "SSH/Agent协议")
  Rel(backend, 外部数据库, "JDBC/TCP")  
  Rel(task_scheduler, platform_db, "存储测试结果", "JDBC")
  Rel(backend, platform_db, "读取数据", "SQL")
  Rel(backend, web_ui, "返回报告", "JSON/HTTP")
```

**容器级技术选型**：

| 容器         | 技术栈       | 选型理由                                                                                     |
| ------------ | ------------ | -------------------------------------------------------------------------------------------- |
| Web前端      |              |                                                                                              |
| 后端服务     | Go           | 高并发特性满足**≥10台并发测试**需求，编译部署特性符合容器化轻量要求                          |
| 任务调度引擎 | Celery+Redis | 分布式架构支持**任务失败重试机制**，Redis持久化队列保障可靠性                                |
| 平台数据库   | MySQL        | 成熟事务支持确保测试数据完整性，与业务需求中的**被测数据库类型**形成技术栈统一，降低运维成本 |

## 3 组件图

> **适用场景**：跨团队协作，主要给开发团队设计代码结构，或排查模块间依赖问题。

```mermaid
%% C4 Level 3: 组件图
C4Component
  title 后端服务组件图

  Container(web_ui, "Web前端")
  Container_Boundary(backend, "后端服务") {
    Component(host_mgr, "主机管理服务", "Go", "Agent注册/SSH密钥管理")
    Component(collector, "数据采集服务", "Python", "执行ansible/shell脚本")
    Component(test_executor, "测试执行引擎", "Go", "执行测试套件")
    Component(report_engine, "报告生成服务", "Jinja2+PDFlib", "生成结构化报告")
  }
  Container(scheduler, "任务调度引擎")
  ContainerDb(platform_db, "平台数据库", "MySQL", "存储测试数据/配置")

  Rel(host_mgr, platform_db, "存储主机配置", "JDBC")
  Rel(collector, scheduler, "提交采集任务", "Celery Task") 
  Rel(test_executor, scheduler, "执行基准测试", "Celery Worker")
  Rel(report_engine, platform_db, "读取测试数据", "SQL查询")
  Rel(report_engine, web_ui, "返回报告", "JSON/HTTP")
```

## 4 部署视图

```mermaid
%% 部署视图（扩展）
flowchart TD
  subgraph Docker Host
    web[web:8080] --> api[backend:8000]
    api --> celery_worker[celery:8899]
    celery_worker -.- redis[redis:6379]
    api -.- mysql[mysql:3306]
  end
```

## 5 业务流程

```mermaid
sequenceDiagram
  用户->>Web前端: 新建测试任务（选择主机/测试项）
  Web前端->>后端服务: POST /api/tasks
  后端服务->>任务调度引擎: 创建Celery任务
  任务调度引擎->>被测主机: 并行发起SSH连接
  被测主机-->>任务调度引擎: 返回实时指标
  任务调度引擎->>平台数据库: 持久化原始数据
  平台数据库-->>后端服务: 生成报告数据
  后端服务->>Web前端: 返回任务进度
  Web前端->>用户: 显示实时状态
```

# 三 技术选型

## 1 前端

### 1.1 框架选型

| 维度/框架      | React                   | Vue          | Angular           |
| -------------- | ----------------------- | ------------ | ----------------- |
| 核心范式       | 函数式编程              | 声明式响应式 | 面向对象          |
| 包体积         | 42KB (React + ReactDOM) | 33KB         | 143KB (全包)      |
| 开发速度       | ★★★★☆                   | ★★★★★        | ★★★☆☆             |
| 大型项目维护性 | ★★★★☆                   | ★★★☆☆        | ★★★★★             |
| 虚拟DOM        | ✓                       | ✓            | ✓                 |
| SSR支持        | Next.js                 | Nuxt3        | Angular Universal |
| TS支持度       | 原生支持                | 类型推导优化 | 原生支持          |
| 性能基准       | 900ms (JS Framework)    | 1050ms       | 1200ms            |
| 移动端适配     | React Native            | Weex/Uni-app | Ionic             |
| 企业级功能需求 | 需组合生态              | 需规范约束   | 开箱即用          |
| 团队学习成本   | 中等（JSX）             | 较低（模板） | 较高（TS）        |
| 状态管理       | Redux/Zustand           | Pinia        | RxJS/NgRx         |
| UI组件库       |                         |              |                   |

### 1.2 部署选型

| 评估维度         | Nginx+后端          | Web框架集成      | Serverless       | 容器化部署    |
| ---------------- | ------------------- | ---------------- | ---------------- | ------------- |
| **性能**         | ★★★★★               | ★★★☆☆            | ★★★★☆            | ★★★★☆         |
| **并发能力**     | 10万+ QPS           | 1万 QPS          | 按需扩展         | 弹性扩展      |
| **部署复杂度**   | 中等（需配置Nginx） | 简单（框架内置） | 极简（一键部署） | 复杂（需K8s） |
| **​运维成本**    | 需维护Nginx配置     | 无需额外维护     | 无需运维         | 需K8S集群     |
| **扩展能力**     | 强（水平扩展）      | 弱（单体架构）   | 自动扩展         | 弹性扩展      |
| **适用团队规模** | 中大型团队          | 小型团队         | 初创团队         | 专业运维团队  |
| **典型成本**     | 服务器费用          | 服务器费用       | 按使用量计费     | 基础设施+运维 |

### 1.3 选型决策
- 前端框架：React
- 部署方案：静态文件+nginx，并打包成docker镜像，作为独立的前端服务

# 四 核心模块

## 1 前端

### 1.1 功能模块
- 测试设备管理
  - 添加设备
    - 输入ssh连接凭证：密码框采用掩码显示
    - 验证连接凭证是否有效：后端验证，前端实时显示验证过程
  - 查看设备
    - 显示已添加设备列表：采用分页加载处理
  - 编辑设备
    - 修改设备连接凭证
      - 只修改密码和端口，不修改ip
    - 复制设备连接凭证
      - 复制后直接进入编辑页面
      - 复制内容不包括口令
  - 删除设备：确认弹窗，提示用户删除影响（如存在关联在用任务）
- 测试任务管理
  - 创建任务
    - 添加测试设备：支持多选，可通过标签或分组选中
    - 选择测试指标（一般根据设备类型自动带出，如主机，数据库实例等）
    - 提交测试任务：显示提交反馈（成功，失败）
  - 查看任务
    - 查看任务配置
    - 查看任务执行记录
      - 支持区分状态：等待中、进行中、完成、失败
  - 编辑任务
    - 复制任务：复制已有任务配置后直接进入编辑页面
    - 修改任务
  - 执行任务
    - 选中执行记录，点击“执行”按钮触发执行
    - 新建任务保存时，用户选择保存并执行
    - 支持任务实时状态反馈
  - 删除任务
    - 只允许删除任务配置，不允许删除任务执行记录
- 测试报告管理
  - 报告模版设置
    - 支持设置标题，logo（如支持富文本）
    - 支持效果预览
  - 查看报告
    - 查看路径：任务执行记录-报告按钮-报告详情
  - 导出报告
    - 后端导出
    - 导出格式：pdf

### 1.2 项目结构

# 五 实施计划

待补充