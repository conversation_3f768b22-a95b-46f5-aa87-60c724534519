# redux 环境搭建

## 配套工具
- 在react中使用redux，官方要求安装两个插件：Redux Toolkit 和 react-redux。
    - ==React Toolkit（RTK）==- 官方推荐编写redux逻辑的方式，是一套工具的集合集，简化书写方式。
        - 简化store的配置方式
        - 内置immer支持可变式状态修改
        - 内置thunk支持更好的异步创建
    - ==react-redux==- 用来链接 Redux 和 React 组件的中间件。
        - ![image](https://img2024.cnblogs.com/blog/1548975/202409/1548975-20240908114137828-433184856.png)

## 配置基础环境
- 使用CRA快速创建 React 项目：`npx create-react-app react-redux`
- 安装配置工具：`npm i @reduxjs/toolkit react-redux`
- 启动项目：`npm run start`

## 目录结构设计

### store目录
- 通常集中状态管理的部分都会单独创建一个单独的store目录
- 应用通常会有很多个子store的模块，所以创建一个modules目录，在内部编写好业务分类的子store
- store中的入口文件 index.js 的作用是组合modules中所有的子模块，并导出store