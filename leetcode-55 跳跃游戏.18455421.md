给你一个非负整数数组 nums ，你最初位于数组的 第一个下标 。数组中的每个元素代表你在该位置可以跳跃的最大长度。
判断你是否能够到达最后一个下标，如果可以，返回 true ；否则，返回 false 。

示例 1：
输入：nums = [2,3,1,1,4]
输出：true
解释：可以先跳 1 步，从下标 0 到达下标 1, 然后再从下标 1 跳 3 步到达最后一个下标。

示例 2：
输入：nums = [3,2,1,0,4]
输出：false
解释：无论怎样，总会到达下标为 3 的位置。但该下标的最大跳跃长度是 0 ， 所以永远不可能到达最后一个下标。
 
提示：
1 <= nums.length <= 104
0 <= nums[i] <= 105

实现：
```go
func canJump(nums []int) bool {

    // 定义能够达到的最大长度，初始值为0
    maxReach := 0

    // 开始遍历nums
    for i:=0; i<len(nums); i++ {

        // 如果当前位置大于maxReach，说明无法达到，则直接返回false
        if i > maxReach {
            return false
        }
        // 每次遍历更新maxReach，取maxReach与当前下标及下标值和的较大值
        maxReach = max(maxReach, i + nums[i])
    }

    // 因为nums遍历完成，说明下标i始终不大于maxReach，即maxReach不小于最后一个下标，所以直接返回true
    return true
}

// 辅助函数
func max(a, b int) int {
    if a > b {
        return a
    } else {
        return b
    }
}
```