# useEffect依赖项

## 参数说明
- useEffect副作用函数的执行时机存在多种情况，根据传入依赖项的不同，会有不同的执行表现。
    - 没有依赖项：组件初始渲染 + 组件更新时执行
    - 空数组依赖：只在初始渲染时执行一次
    - 添加特定依赖项：组件初始渲染 + 特性依赖项变化时执行

## 示例
- 
```js
import { useEffect, useState } from "react"

function App() {

  // 1、没有依赖项，初始 + 组件更新
  const [count, setCount] = useState(0)
  useEffect(() => {
    console.log('副作用函数执行了')
  })

  // 2、传入空数组依赖 初始执行一次
  useEffect(() => {
    console.log('副作用函数执行了')
  }, [])

  // 3、传入特定依赖项 初始 + 依赖项变化时执行
  useEffect(() => {
    console.log('副作用函数执行了')
  }, [count])

  return (
    <div>
      this is App
      <button onClick={() => setCount(count + 1)}>+{count}</button>
    </div>
  );
}

export default App;

```
