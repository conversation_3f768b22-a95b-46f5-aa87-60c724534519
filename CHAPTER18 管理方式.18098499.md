# 小商品工厂与电影工作组
两种主流软件公司组织结构：小商品工厂（widget factory）和电影工作组（film crews）。

## widget factory 管理方式
**背景**
由于天性懒惰，每个人都希望工作越少越好，只要有可能就会逃避工作，并对组织目标不感兴趣。因此，管理者需要通过各种强制手段来激发团队工作动力。
**特点**
widget factory 公司注重资源、流程、运营效率、一致性及可重复性，严格控制资源使用，有鲜明的工作角色定位和流程定义，对实实在在的软件开发并不关心，真正重视的是软件开发的蓝图。
PM和BA在组织中占据重要角色。

## film crew 管理方式

**背景**
员工具有相当高的智力和创造力，是可以自我激发、自我调节和自我监督的，并且享受工作。个体所具有的专业能力远优于被组织培养出来的能力，经理不再能代替每个人，树状层级结构也无法高效运作，人们需要通过比较复杂的形式合作才能把事情完成。

**特点**
film crew 主管负责鼓舞士气，守护愿景，提供方向，以及集中所有人的精力。相信软件成果归功于所有参与者即独特的团队工作方式。

## 两种管理方式对比
* 报酬方面，widget factory 下PM，BA有更高报酬，因为这类岗位被认为在业务角度价值产出更高，而 film crew 价值更多基于人的能力而不是角色。
* 信息透明度方面，widget factory 中，PM和BA会努力维护自身的权益，不会让普通员工得到项目的原始信息；而 film crew 则不限制项目成员获取主要的原始信息，在该模式下，PM不太是一个有创造力的领导者，而是弱化为行政管理上的支持者，而BA的部分工作则直接被团队所取代。
* 开发流程方面，widget factory 主要依赖流程的管理模式，需要层层审批，而 film crew 更主张平等的工作职能，每个人都能实现形成自己的价值判断，自由选择实现团队愿景的方式。

# 行之有效的敏捷方法
敏捷发起人 Martin Fowler 表示，在敏捷开发中，由于存在语义扩散问题，甚至导致人们在项目中无法辨别出敏捷方法的影子。这是因为敏捷方法中很多概念不直观，很多人没有真正理解敏捷方法，也就无法正确地运用与实践，从而不知道自己能否从中受益。

## 敏捷方法与计划驱动方法的区别
* 敏捷方法：强调自适应计划，以人为本和沟通。
	* 自适应计划 - 能缩短计划的周期。
	* 以人为本 - 让参与软件开发的人员自己来定义和选择合适的流程。
	* 沟通 - 敏捷方法的核心，包括单元测试、功能测试、故事墙、回顾、功能演示、持续集成等。
* 计划驱动方法：基于预测，项目成功的标准是按计划、按时、按预算完成工作。

区别：软件开发领域无法保持计划驱动方法的传统，即事先定义好方法和流程并在工作中严格执行；而敏捷则是提倡让开发人员来定义和选择适合自己的流程，这点其实也是对人员素质提出了更高的要求。

## 沟通
* 单元测试：程序员与代码组件之间的沟通；
* 功能测试：程序员、QA和系统之间的沟通；
* 故事墙和回顾：团队成员之间的沟通；
* 功能演示：企业通过产品和最终用户之间的沟通；
* 持续集成：产品和企业计算环境的沟通。

# 影响软件质量的潜在因素
* 始终从用户角度出发：质量管理目标就是实现用户需求，所以需要通过质量管理来管理软件和与用户的关系，并基于用户需求管理整个团队的沟通有效性。
* 领导能力：领导者需要建立一个团结一致且方向明确的团队，还要创造并维护相互信任的内部气氛，使得所有人都能参与实现整个团队的目标。
* 团队成员的主动参与性：只有不同分工和职责的团队成员都参与进来，整个项目或是整个软件的各个部分和各个方面才会得到完美的实现。
* 流程方法：指通过有效率的流程或方法，把所有的资源和日常工作整合在一起，形成一种流水线式的生产模式。
* 团队系统管理：即确定、理解并管理一个与团队相关的系统流程，以使整个团队能够有效并快速地自我改善。
* 持续改进：工作效率的改进很大程度上取决于工作流程的改进，所以流程改进需要长期进行，比如采用 “计划-执行-检查-总结”（PDCA）的循环方式。
* 基于事实做决策：注意收集日常数据和信息，并对它们的精确性进行测量，以保证决策依据的正确性。
* 互惠互利：即便部门和子团队职能是独立的，但互惠互利的企业文化可以增强公司整体实力并实现长远价值。

# 细说分工
在分工上不能盲目认为存在即合理，而要尽可能独立思考。

## 分工的优点和缺点
* 优点：降低专业技能门槛，使很多工作得以并行执行，也使很多重复劳动可以通过技术手动来解决，进而提高生产力。
* 缺点：分工过细，简单重复的工作会让人变成不会思考的机器，不能完全领会工作目标而返工，还会带来沟通和协同成本，进而影响生产力的提升。

## 全球化背景下的分工
全球化分工既能带来机会，也会带来冲击。
机会：比如通过参加落地测试，普通技术人员有机会参与技术含量更高的复杂项目。
冲击：用人成本不断攀升，跨国企业岗位迁往拥有廉价劳动力的国家。
经验：为了在全球化分工中获得高附加值，产业角度需要从劳动密集型转往知识密集型，个人发展角度需要技能架构从单一技能型发展为复合技能型。

## 分工的不同侧面
* 为了追求更快速，更低成本的生产，有的企业会选择将业务层层外包，从而导致产品质量失控；
* 当规模不断扩张时，有的企业受人数所迫，将工作和任务分的越来越细，从而出现人浮于事的现象；
* 现实情况是，每当有新技术出现时，一些复杂的工序最终会由一台机器或是一种先进的技术来完成，例如机器生产之于手工生产，自动化生产之于传统手工生产，AIGC创作之于原创输出。

## 更好的分工
现代公司主要分工模式：
**控制型分工模式**
- 企业基于工作技能进行分工，员工会被分配到一个比较窄的技能领域去完成非常明确的工作。
- 控制型公司会对技术人员做出细致地划分，例如按照编程语言划分（web端，ios端，算法，数据等），或按照岗位职责划分（研发，测试，运维等）等。

**责任型分工模式**
- 企业面向员工的责任心和所承担的目标来分配工作，员工完成工作时需要的不仅是技能，还有更强的责任感。
- 责任型公司会将员工统称为软件工程师，即不是只具备特定技能的编程人员，而是学习了多种语言和技术，能够保证软件质量并对软件进行维护的全能型人才。这样的软件工程师可以加入各种团队，企业则根据软件功能、模块或产品线来灵活调整分工。