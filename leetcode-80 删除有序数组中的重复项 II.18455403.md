给你一个有序数组 nums ，请你 原地 删除重复出现的元素，使得出现次数超过两次的元素只出现两次 ，返回删除后数组的新长度。
不要使用额外的数组空间，你必须在 原地 修改输入数组 并在使用 O(1) 额外空间的条件下完成。 

说明：
为什么返回数值是整数，但输出的答案是数组呢？
请注意，输入数组是以「引用」方式传递的，这意味着在函数里修改输入数组对于调用者是可见的。
你可以想象内部操作如下:

// nums 是以“引用”方式传递的。也就是说，不对实参做任何拷贝
int len = removeDuplicates(nums);

// 在函数里修改输入数组对于调用者是可见的。
// 根据你的函数返回的长度, 它会打印出数组中 该长度范围内 的所有元素。
for (int i = 0; i < len; i++) {
    print(nums[i]);
}
 
示例 1：
输入：nums = [1,1,1,2,2,3]
输出：5, nums = [1,1,2,2,3]
解释：函数应返回新长度 length = 5, 并且原数组的前五个元素被修改为 1, 1, 2, 2, 3。 不需要考虑数组中超出新长度后面的元素。

示例 2：
输入：nums = [0,0,1,1,1,1,2,3,3]
输出：7, nums = [0,0,1,1,2,3,3]
解释：函数应返回新长度 length = 7, 并且原数组的前七个元素被修改为 0, 0, 1, 1, 2, 3, 3。不需要考虑数组中超出新长度后面的元素。

提示：
1 <= nums.length <= 3 * 104
-104 <= nums[i] <= 104
nums 已按升序排列

实现：
```go
func removeDuplicates(nums []int) int {

	// 如果nums元素个数不大于2个，则无需比较，直接返回nums长度
	n := len(nums)
	if n <= 2 {
		return n
	}

	// 定义快慢指针，初始值为2，表示从nums第3个元素开始比较
	slow := 2

	// 开始比较
	for fast:=2; fast < n; fast++ {
		// 如果快指针元素与慢指针前两个元素一样，因为是顺序数组，说明重复元素超过2个，此时快指针不做任何操作，继续往前遍历，即进入下一轮循环

        // 如果快指针元素与慢指针前两个元素不一样，说明快指针值符合新数组构造要求，所以将快指针值赋值给慢指针，同时慢指针前进一位
		if nums[fast] != nums[slow-2] {
			nums[slow] = nums[fast]
			slow++
		
		}
	}

	// 快指针遍历结束后，此时慢指针值表示的即是数组长度
    // 说明：当快指针完成最后一次新数组赋值时，慢指针会前进一位，即指向下一个要插值的位置，但因为快指针已经是最后一次赋值，所以实际慢指针是走多了一步的，所以虽然慢指针是从0开始计数，但其最终值实际代表的就是新数组队列长度
	return slow

}
```