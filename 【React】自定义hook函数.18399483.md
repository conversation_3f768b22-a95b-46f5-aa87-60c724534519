# 自定义hook函数

## 概念
- 自定义hook是以use打头的函数，通过自定义hook函数可以用来实现逻辑的封装和复用。

## 思路
- 1. 声明一个use打头的函数
- 2. 在函数体内封装可以复用的逻辑（只要是可复用的逻辑）
- 3. 在组件中用到的状态或者回调return出去（以对象或数组）
- 4. 在哪个组件中要用到这个逻辑，就执行这个函数，解构出来状态和回调进行使用

## 使用规则
- 只能在组件中或者其他自定义hook函数中调用
- 只能在组件的顶层调用，不能嵌套在if，for，以及其他函数中

## 实现


### 自定义hook函数格式
- 
```js
function useXxx() {
  // 状态逻辑
  return {
    // 状态
    // 方法
  }
}
```

### 不封装直接实现
- 
```js
import { useEffect, useState } from "react"

function App() {

  const [value, setValue] = useState(true)

  const toggle = () => setValue(!value)

  return (
    <div>
      {value && <div>this is div</div>}
      <button onClick={toggle}>toggle</button>
    </div>
  );
}

export default App;

```

### 封装自定义hook实现
- 
```js
import { useState } from "react"

function useToggle() {

  // 可复用的代码逻辑
  const [value, setValue] = useState(true)
  const toggle = () => setValue(!value)

  // 需要在其他组件中使用的状态和回调函数使用return出来
  return [value, toggle]
}

function App() {

  const [value, toggle] = useToggle()

  return (
    <div>
      {value && <div>this is div</div>}
      <button onClick={toggle}>toggle</button>
    </div>
  );
}

export default App;
```